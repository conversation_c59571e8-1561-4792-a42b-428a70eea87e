#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
表格处理 Schema
"""

from typing import Optional, List, Dict, Any, Union
from pydantic import BaseModel, Field
import uuid

class TableColumnDefinition(BaseModel):
    """表格列定义"""
    name: str = Field(..., description="列名")
    field_key: str = Field(..., description="字段键名")
    data_type: str = Field("string", description="数据类型: string, number, date, boolean")
    required: bool = Field(False, description="是否必填")
    validation_rules: Optional[Dict[str, Any]] = Field(None, description="验证规则")
    description: Optional[str] = Field(None, description="列描述")

class TableTemplate(BaseModel):
    """表格模板定义"""
    template_id: str = Field(..., description="模板ID")
    template_name: str = Field(..., description="模板名称")
    description: Optional[str] = Field(None, description="模板描述")
    columns: List[TableColumnDefinition] = Field(..., description="列定义")
    identification_rules: List[str] = Field(..., description="模板识别规则")
    processing_rules: Optional[Dict[str, Any]] = Field(None, description="处理规则")

class TableProcessingRequest(BaseModel):
    """表格处理请求"""
    file_id: uuid.UUID = Field(..., description="文件ID")
    processing_mode: str = Field("auto", description="处理模式: auto, template_only, ai_only")
    template_hint: Optional[str] = Field(None, description="模板提示")
    custom_prompt: Optional[str] = Field(None, description="自定义AI提示词")
    
    # AI配置
    use_default_vision_model: bool = Field(True, description="使用默认视觉模型")
    vision_model_id: Optional[uuid.UUID] = Field(None, description="指定视觉模型ID")
    vision_temperature: Optional[float] = Field(None, description="视觉模型温度")
    vision_max_tokens: Optional[int] = Field(None, description="视觉模型最大token数")
    additional_messages: Optional[List[Dict[str, Any]]] = Field(None, description="额外的消息历史，用于多轮对话上下文")
    
    # 验证配置
    enable_validation: bool = Field(True, description="启用数据验证")
    enable_correction: bool = Field(True, description="启用数据修正")
    
class TableProcessingResult(BaseModel):
    """表格处理结果"""
    success: bool = Field(..., description="处理是否成功")
    processing_method: str = Field(..., description="实际使用的处理方法: template, ai, hybrid")
    template_matched: Optional[str] = Field(None, description="匹配的模板ID")
    
    # 提取的数据
    extracted_data: List[Dict[str, Any]] = Field(..., description="提取的数据")
    column_mapping: Dict[str, str] = Field(..., description="列映射关系")
    
    # 处理统计
    total_rows: int = Field(0, description="总行数")
    valid_rows: int = Field(0, description="有效行数")
    error_rows: int = Field(0, description="错误行数")
    
    # 错误和警告
    errors: List[Dict[str, Any]] = Field(default_factory=list, description="错误列表")
    warnings: List[Dict[str, Any]] = Field(default_factory=list, description="警告列表")
    
    # AI处理信息（如果使用了AI）
    ai_processing_info: Optional[Dict[str, Any]] = Field(None, description="AI处理信息")
    
class TableValidationError(BaseModel):
    """表格验证错误"""
    row_index: int = Field(..., description="行索引")
    column: str = Field(..., description="列名")
    error_type: str = Field(..., description="错误类型")
    error_message: str = Field(..., description="错误信息")
    original_value: Any = Field(None, description="原始值")
    suggested_value: Any = Field(None, description="建议值")

class TableCorrectionSuggestion(BaseModel):
    """表格修正建议"""
    row_index: int = Field(..., description="行索引")
    column: str = Field(..., description="列名") 
    original_value: Any = Field(..., description="原始值")
    corrected_value: Any = Field(..., description="修正值")
    confidence: float = Field(..., description="置信度", ge=0.0, le=1.0)
    correction_reason: str = Field(..., description="修正原因")

class TemplateMatchResult(BaseModel):
    """模板匹配结果"""
    matched: bool = Field(..., description="是否匹配")
    template_id: Optional[str] = Field(None, description="匹配的模板ID")
    confidence: float = Field(0.0, description="匹配置信度", ge=0.0, le=1.0)
    match_details: Dict[str, Any] = Field(default_factory=dict, description="匹配详情")

class AIExtractionRequest(BaseModel):
    """AI提取请求"""
    image_data: str = Field(..., description="图像数据(base64)")
    prompt: str = Field(..., description="提取提示词")
    expected_columns: Optional[List[str]] = Field(None, description="期望的列名")
    response_format: str = Field("json", description="响应格式")

class AIExtractionResult(BaseModel):
    """AI提取结果"""
    success: bool = Field(..., description="提取是否成功")
    extracted_data: List[Dict[str, Any]] = Field(..., description="提取的数据")
    confidence: float = Field(0.0, description="整体置信度", ge=0.0, le=1.0)
    processing_info: Dict[str, Any] = Field(default_factory=dict, description="处理信息")
    
class TableProcessingConfig(BaseModel):
    """表格处理配置"""
    max_rows: int = Field(1000, description="最大处理行数")
    max_columns: int = Field(50, description="最大处理列数") 
    ai_retry_count: int = Field(2, description="AI重试次数")
    template_confidence_threshold: float = Field(0.8, description="模板匹配置信度阈值")
    validation_strict_mode: bool = Field(False, description="严格验证模式")
    auto_correction_enabled: bool = Field(True, description="自动修正功能")
    
class ProcessingStats(BaseModel):
    """处理统计"""
    start_time: str = Field(..., description="开始时间")
    end_time: str = Field(..., description="结束时间")
    processing_duration: float = Field(..., description="处理耗时(秒)")
    template_match_time: Optional[float] = Field(None, description="模板匹配耗时")
    ai_processing_time: Optional[float] = Field(None, description="AI处理耗时")
    validation_time: Optional[float] = Field(None, description="验证耗时")
    total_tokens_used: Optional[int] = Field(None, description="使用的总token数") 