#!/usr/bin/env python3
"""
分拨数据分析脚本
分析AI返回的分拨数据是否准确
"""

import json

def analyze_distribution_data():
    """分析分拨数据"""
    
    # 模拟用户提供的分拨数据
    sample_data = {
        'type': 'store',
        'items': [
            {
                'unit': '公斤',
                'quantity': 10.0,
                'unit_price': 54.0,
                'product_name': '东魁杨梅A果',
                'total_amount': 1026.0,
                'specification': '1.9'
            },
            {
                'unit': '公斤',
                'quantity': 6.0,
                'unit_price': 46.0,
                'product_name': '云南杨梅',
                'total_amount': 524.4,
                'specification': '1.9'
            }
        ]
    }

    print('=' * 60)
    print('🔍 分拨数据分析')
    print('=' * 60)
    
    total_errors = 0
    
    for i, item in enumerate(sample_data['items'], 1):
        product_name = item['product_name']
        quantity = item['quantity']
        unit_price = item['unit_price']
        specification = float(item['specification'])
        total_amount = item['total_amount']
        
        # 计算预期金额：规格 × 数量 × 单价
        expected_amount = specification * quantity * unit_price
        
        # 计算差异
        difference = abs(total_amount - expected_amount)
        is_correct = difference < 0.01  # 允许0.01的浮点误差
        
        print(f'📦 商品 {i}: {product_name}')
        print(f'   规格: {specification} | 数量: {quantity} | 单价: ¥{unit_price}')
        print(f'   AI返回金额: ¥{total_amount}')
        print(f'   预期金额: ¥{expected_amount:.2f}')
        print(f'   差异: ¥{difference:.2f}')
        print(f'   状态: {"✅ 正确" if is_correct else "❌ 错误"}')
        print()
        
        if not is_correct:
            total_errors += 1
    
    print('=' * 60)
    print(f'📊 分析结果')
    print(f'   总商品数: {len(sample_data["items"])}')
    print(f'   错误商品数: {total_errors}')
    print(f'   准确率: {((len(sample_data["items"]) - total_errors) / len(sample_data["items"]) * 100):.1f}%')
    
    if total_errors > 0:
        print('\n🔧 问题分析:')
        print('   1. AI可能在计算金额时没有正确使用规格字段')
        print('   2. 可能直接使用 数量 × 单价，而忽略了规格')
        print('   3. 需要检查AI提示词中的计算公式说明')
    
    return total_errors == 0

if __name__ == "__main__":
    analyze_distribution_data() 