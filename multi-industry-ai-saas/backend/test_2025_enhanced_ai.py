#!/usr/bin/env python3
"""
2025年增强AI采购分拨单识别测试
测试最新的视觉AI技术在采购分拨单识别中的应用
"""

import asyncio
import json
import logging
import uuid
import os
import base64
import time
from pathlib import Path

# 设置环境
import sys
sys.path.append('/app')

from services.enhanced_table_processing_service import Enhanced2025TableProcessingService
from tasks.enhanced_2025_purchase_order_task_executor import Enhanced2025PurchaseOrderTaskExecutor
from models.storage import StorageFile
from models.task import AsyncTask
from schemas.table_processing import TableProcessingRequest
from db.database import AsyncSessionLocal
from sqlalchemy import select

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 使用真实的文件信息
TEST_FILE_ID = "d09595ab-5c4b-4231-bfd0-dc30e697d1b3"
TEST_PROJECT_ID = "*************-48ab-8092-e8eb7f663677"
TEST_USER_ID = "04a15201-2024-4d45-b434-a0bb4ff40c34"

async def test_2025_enhanced_ai():
    """测试2025年增强AI系统"""
    
    print("=" * 80)
    print("🚀 2025年增强AI采购分拨单识别测试")
    print("=" * 80)
    print(f"文件ID: {TEST_FILE_ID}")
    print(f"项目ID: {TEST_PROJECT_ID}")
    print(f"用户ID: {TEST_USER_ID}")
    
    async with AsyncSessionLocal() as db:
        base64_image_string = None
        physical_path = None
        
        try:
            # 验证文件并加载图片
            print("\n🔍 Step 1: 验证文件信息并加载图片...")
            
            stmt = select(StorageFile).where(
                StorageFile.id == uuid.UUID(TEST_FILE_ID),
                StorageFile.project_id == uuid.UUID(TEST_PROJECT_ID)
            )
            result = await db.execute(stmt)
            file_info = result.scalar_one_or_none()
            
            if file_info:
                print(f"✅ 文件名: {file_info.name}")
                base_upload_path = "/app/uploads"
                physical_path = base_upload_path + file_info.storage_path
                if os.path.exists(physical_path):
                    print(f"✅ 物理文件存在: {physical_path}")
                    with open(physical_path, "rb") as image_file:
                        base64_image_string = base64.b64encode(image_file.read()).decode('utf-8')
                    print(f"✅ 图片已加载并编码为Base64 (长度: {len(base64_image_string)})")
                else:
                    print(f"❌ 物理文件不存在: {physical_path}")
                    return None
            else:
                print("❌ 数据库中文件记录不存在")
                return None
                
        except Exception as e:
            print(f"❌ 验证文件或加载图片失败: {e}")
            import traceback
            traceback.print_exc()
            return None
        
        if not base64_image_string:
            print("❌ 未能获取图片的Base64编码字符串，测试中止。")
            return None
        
        print("\n" + "=" * 80)
        
        try:
            # 测试2025年增强表格处理服务
            print("🧠 Step 2: 测试2025年增强表格处理服务...")
            
            start_time = time.time()
            
            # 创建处理请求
            request = TableProcessingRequest(
                file_id=uuid.UUID(TEST_FILE_ID),
                user_id=uuid.UUID(TEST_USER_ID),
                project_id=uuid.UUID(TEST_PROJECT_ID),
                processing_mode="enhanced_2025_ai"
            )
            
            enhanced_service = Enhanced2025TableProcessingService()
            service_result = await enhanced_service.process_table_with_enhanced_ai(request, file_info)
            
            processing_time = time.time() - start_time
            
            print(f"⏱️  处理时间: {processing_time:.2f}秒")
            print(f"📊 2025年增强服务处理结果: {service_result.get('success')}")
            
            if service_result.get('success'):
                data = service_result['data']
                metadata = service_result.get('metadata', {})
                
                print(f"✅ 识别的商品数量: {data.get('product_count', 0)}")
                print(f"✅ 识别的门店数量: {data.get('store_count', 0)}")
                
                # 显示表格结构信息
                table_structure = metadata.get('table_structure', {})
                print(f"\n📋 表格结构信息:")
                print(f"  类型: {table_structure.get('type')}")
                print(f"  置信度: {table_structure.get('confidence', 0):.3f}")
                print(f"  行数: {table_structure.get('rows')}")
                print(f"  列数: {table_structure.get('columns')}")
                print(f"  商品列: {len(table_structure.get('product_columns', []))}")
                print(f"  门店列: {len(table_structure.get('store_columns', []))}")
                
                # 显示质量评估
                quality_assessment = metadata.get('quality_assessment', {})
                print(f"\n🎯 质量评估:")
                print(f"  完整性评分: {quality_assessment.get('completeness_score', 0):.3f}")
                print(f"  准确性置信度: {quality_assessment.get('accuracy_confidence', 0):.3f}")
                print(f"  门店识别率: {quality_assessment.get('store_recognition_rate', 0):.3f}")
                print(f"  质量等级: {quality_assessment.get('quality_grade', 'unknown')}")
                
                # 显示商品信息
                products = data.get('products', [])
                print(f"\n📦 识别的商品 (前5个):")
                for i, product in enumerate(products[:5]):
                    print(f"  {i+1}. {product.get('name')} - 规格: {product.get('specification')} - 单价: {product.get('unit_price')}")
                    if product.get('suggested_category'):
                        print(f"      推荐分类: {product.get('suggested_category')}")
                    if product.get('suggested_brand'):
                        print(f"      推荐品牌: {product.get('suggested_brand')}")
                
                # 显示门店分拨信息
                distribution = data.get('distribution', {})
                print(f"\n🏪 门店分拨信息 (前3个门店):")
                for store_name, store_products in list(distribution.items())[:3]:
                    print(f"  {store_name}: {len(store_products)} 个商品")
                    for product in store_products[:2]:
                        print(f"    - {product.get('product_name')}: {product.get('quantity')} {product.get('specification', '')}")
            else:
                print(f"❌ 2025年增强服务处理失败: {service_result.get('error')}")
            
            print("\n" + "=" * 80)
            
            # 测试2025年增强任务执行器
            print("⚡ Step 3: 测试2025年增强任务执行器...")
            
            start_time = time.time()
            
            # 创建模拟的AsyncTask对象
            mock_task = AsyncTask(
                id=str(uuid.uuid4()),
                project_id=uuid.UUID(TEST_PROJECT_ID),
                tenant_id=uuid.UUID(TEST_PROJECT_ID),
                user_id=uuid.UUID(TEST_USER_ID),
                task_type="purchase_order_2025_ai",
                task_name="2025年增强版采购订单处理测试",
                input_data={
                    "file_id": TEST_FILE_ID,
                    "upload_type": "allocation_slip",
                    "warehouse_id": None,
                    "processing_mode": "enhanced_2025_ai",
                    "task_params": {
                        "use_default_vision_model": True,
                        "vision_temperature": 0.01,
                        "vision_max_tokens": 16384
                    }
                }
            )
            
            # 实例化2025年增强版执行器
            executor = Enhanced2025PurchaseOrderTaskExecutor()
            print(f"Enhanced2025PurchaseOrderTaskExecutor 已实例化 (Task ID: {mock_task.id})")
            
            # 测试execute_task方法
            print("开始调用 Enhanced2025PurchaseOrderTaskExecutor.execute_task...")
            
            task_result = await executor.execute_task(mock_task, test_mode=True)
            
            execution_time = time.time() - start_time
            print(f"⏱️  执行时间: {execution_time:.2f}秒")
            
            # 显示详细结果
            print("\n" + "=" * 80)
            print("📊 2025年增强版任务执行器处理结果")
            print("=" * 80)
            
            if task_result.get('success'):
                print(f"✅ 成功: 2025年增强版执行器返回了数据")
                frontend_data = task_result['data']
                metadata = task_result.get('metadata', {})
                
                # 显示前端数据统计
                statistics = frontend_data.get('statistics', {})
                quality = frontend_data.get('recognition_quality', {})
                
                print(f"\n📈 前端数据统计:")
                print(f"  总门店数: {statistics.get('total_stores')}")
                print(f"  总商品数: {statistics.get('total_products')}")
                print(f"  总金额: ¥{statistics.get('total_amount', 0):,.2f}")
                print(f"  AI版本: {statistics.get('ai_version')}")
                
                print(f"\n🎯 识别质量:")
                print(f"  商品识别率: {quality.get('product_recognition_rate')}%")
                print(f"  门店识别率: {quality.get('store_recognition_rate')}%")
                print(f"  数据完整性: {quality.get('data_completeness')}")
                print(f"  完整性评分: {quality.get('completeness_score', 0)}")
                print(f"  质量等级: {quality.get('quality_grade')}")
                
                # 显示处理元数据
                print(f"\n🔧 处理元数据:")
                print(f"  执行器版本: {metadata.get('executor_version')}")
                print(f"  AI技术: {metadata.get('ai_technology')}")
                print(f"  处理时间: {metadata.get('processing_time', 0):.2f}秒")
                
                # 显示商品信息
                products = frontend_data.get('preview', {}).get('purchase_items', [])
                print(f"\n📦 前端商品数据 (前5个):")
                for i, product in enumerate(products[:5]):
                    print(f"  {i+1}. {product.get('product_name')} - 规格: {product.get('product_specification')} - 单价: ¥{product.get('unit_price', 0)}")
                    print(f"      数量: {product.get('quantity')} - 总金额: ¥{product.get('total_amount', 0):,.2f}")
                    if product.get('suggested_category'):
                        print(f"      推荐分类: {product.get('suggested_category')}")
                
                # 显示门店分拨信息
                distribution = frontend_data.get('preview', {}).get('distribution_destinations', [])
                print(f"\n🏪 前端门店分拨数据 (前3个门店):")
                for i, dest in enumerate(distribution[:3]):
                    print(f"  {i+1}. {dest.get('target_name')}: {len(dest.get('items', []))} 个商品")
                    print(f"      总金额: ¥{dest.get('total_amount', 0):,.2f}")
                    for item in dest.get('items', [])[:2]:
                        print(f"        - {item.get('product_name')}: {item.get('quantity')} × ¥{item.get('unit_price', 0)} = ¥{item.get('total_amount', 0):,.2f}")
                
                # 2025年特有的分析
                print(f"\n🔬 2025年增强分析:")
                ai_confidence_avg = sum(item.get('ai_confidence', 0) for item in products) / len(products) if products else 0
                print(f"  平均AI置信度: {ai_confidence_avg:.3f}")
                
                # 检查智能推荐效果
                products_with_category = sum(1 for p in products if p.get('suggested_category'))
                category_coverage = products_with_category / len(products) if products else 0
                print(f"  智能分类覆盖率: {category_coverage:.1%}")
                
                products_with_brand = sum(1 for p in products if p.get('suggested_brand'))
                brand_coverage = products_with_brand / len(products) if products else 0
                print(f"  智能品牌覆盖率: {brand_coverage:.1%}")
                
                print(f"\n🎉 2025年增强AI测试完成！")
                print(f"总体评价: 商品识别率 {quality.get('product_recognition_rate', 0)}%, 门店识别率 {quality.get('store_recognition_rate', 0)}%")
                
            else:
                print("❌ 失败: 2025年增强版执行器未返回成功结果")
                error_msg = task_result.get('error', '未知错误')
                print(f"错误信息: {error_msg}")
                
                # 显示错误详情
                metadata = task_result.get('metadata', {})
                if 'error_details' in metadata:
                    print(f"错误详情: {metadata['error_details']}")
            
            print("\n=== 2025年增强AI测试完成 ===")
            return task_result
            
        except Exception as e:
            logger.error(f"测试2025年增强版执行器失败: {str(e)}")
            print(f"测试2025年增强版执行器失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return None

async def main():
    """主测试函数"""
    print("🚀 开始2025年增强版表格处理系统测试...")
    print("使用真实文件: " + TEST_FILE_ID)
    print("=" * 80)
    
    # 测试2025年增强系统
    result = await test_2025_enhanced_ai()
    
    print("\n" + "=" * 80)
    print("🎯 2025年增强AI测试完成！")
    
    return result

if __name__ == "__main__":
    print("--- 2025年增强AI系统测试脚本启动 ---")
    try:
        asyncio.run(main())
        print("--- 2025年增强AI系统测试脚本成功完成 ---")
    except Exception as e:
        print(f"!!!!!! 2025年增强AI系统测试中出现未处理异常: {e} !!!!!!")
        import traceback
        print("!!!!!! 异常堆栈: !!!!!!")
        traceback.print_exc()
        print("!!!!!!!!!!!!!!!!!!!!!!!!!")
