#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import logging
import importlib
import sys
from typing import Callable, Awaitable, Dict, Any
from fastapi import FastAPI, Depends, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from contextlib import asynccontextmanager
from sqlalchemy.orm import configure_mappers
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import Response
import uvicorn
import uuid

from db.database import get_db
from db.init_data import init_database
from core.config import settings
from core.tenant_context import TenantContextMiddleware, get_tenant_context
from middleware.operation_log import OperationLogMiddleware
from api import api_router
from plugins.sapi.mcp_router import mcp_router
import models  # 导入所有模型，确保它们都被注册
from schemas.task_execution import TaskExecutionResult
# from core.events import create_start_app_handler, create_stop_app_handler # Commented out
# from middleware.custom_middleware import TenantMiddleware # 假设您有这个 # Commented out
# from middleware.permission_middleware import RbacMiddleware # 假设您有这个

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Explicitly configure the logger used in mcp_proxy.py
mcp_proxy_logger_instance = logging.getLogger("plugins.sapi.mcp_proxy")
mcp_proxy_logger_instance.setLevel(logging.INFO) # Ensure this specific logger is set to INFO
# If basicConfig added a handler to root, and propagation is on (default),
# messages from mcp_proxy_logger_instance should reach it.
# To be absolutely sure it has a handler that outputs:
if not mcp_proxy_logger_instance.handlers: # Add a handler if it doesn't have one
    # This might lead to duplicate logs if basicConfig's handler is also reached via propagation
    # but useful for ensuring output if propagation is disabled or root has no/wrong handlers.
    # _main_handler = logging.StreamHandler(sys.stdout) # Output to stdout
    # _main_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    # _main_handler.setFormatter(_main_formatter)
    # _main_handler.setLevel(logging.INFO)
    # mcp_proxy_logger_instance.addHandler(_main_handler)
    # To avoid potential duplicate logs if basicConfig already handles it well,
    # we can rely on propagation if main logger is correctly set up to output INFO.
    # If issues persist, uncommenting the above handler addition for mcp_proxy_logger_instance might be needed.
    pass # Relying on root logger propagation for now

logger.info("PYTHONPATH (from env): %s", os.environ.get('PYTHONPATH')) # Log PYTHONPATH env var
logger.info("[main.py top] loaded")
logger.info(f"使用SECRET_KEY: {settings.SECRET_KEY[:5]}...")

# 导入任务执行器以确保它们被注册
# import services.purchase_order_task_executor  <- THIS IS THE OLD, INCORRECT ONE
# We will handle registration in the startup_event

@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    应用启动和关闭时的生命周期事件
    """
    logger.info("[main.py lifespan] running")
    # 启动时执行
    logger.info("应用启动中...")

    # 强制重新配置映射器
    configure_mappers()
    logger.info("SQLAlchemy 映射器配置完成")

    await init_database()
    logger.info("数据库初始化完成")

    # 注册任务执行器
    try:
        logger.info("开始注册任务执行器...")
        from services.async_task_service import AsyncTaskService
        logger.info("AsyncTaskService 导入成功")
        
        from tasks.enhanced_purchase_order_task_executor import EnhancedPurchaseOrderTaskExecutor
        logger.info("EnhancedPurchaseOrderTaskExecutor 导入成功")
        
        from models.task import AsyncTask
        from sqlalchemy.ext.asyncio import AsyncSession
        from schemas.task_execution import TaskExecutionResult
        logger.info("相关模块导入成功")

        # 增强版任务执行器适配器
        async def enhanced_purchase_order_executor_adapter(db: AsyncSession, task: AsyncTask) -> TaskExecutionResult:
            logger.info(f"增强版任务执行器调用，任务ID: {task.id}")
            executor_instance = EnhancedPurchaseOrderTaskExecutor()
            
            # 调用增强版执行器的execute_task方法
            result = await executor_instance.execute_task(task, test_mode=False)
            
            # 转换为标准TaskExecutionResult格式
            return TaskExecutionResult(
                success=result.get('success', False),
                data=result
            )

        # 只注册增强版执行器
        logger.info("注册 purchase_order_ai_preview 执行器...")
        AsyncTaskService.register_executor("purchase_order_ai_preview", enhanced_purchase_order_executor_adapter)
        
        registered_executors = list(AsyncTaskService._task_executors.keys())
        logger.info(f"任务执行器注册成功! 已注册的执行器: {registered_executors}")
    except Exception as e:
        logger.error(f"任务执行器注册失败: {e}", exc_info=True)

    # 初始化插件系统
    try:
        from plugins import initialize_plugins
        await initialize_plugins()
        logger.info("插件系统初始化成功")
    except Exception as e:
        logger.error(f"插件系统初始化失败: {str(e)}")
        import traceback
        traceback.print_exc()
        # 不重新抛出异常，允许应用程序继续启动
        # 但是记录错误，以便后续处理

    # 路由打印
    from fastapi.routing import APIRoute
    logger.info("==== FastAPI Registered Routes ====")
    for route in app.routes:
        if isinstance(route, APIRoute):
            logger.info(f"{route.path} [{','.join(route.methods)}] -> {route.name}")
    logger.info("===================================")

    yield
    logger.info("[main.py lifespan] shutdown")

app = FastAPI(
    title="多行业AI SaaS系统API",
    description="支持多租户、多行业的AI SaaS系统API",
    version="1.0.0",
    lifespan=lifespan,
    # on_startup=[create_start_app_handler()], # Commented out
    # on_shutdown=[create_stop_app_handler()], # Commented out
)

# --- BEGIN DetailedLoggingMiddleware ---
class DetailedLoggingMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next: Callable[[Request], Awaitable[Response]]) -> Response:
        print("[DEBUG_PRINT] DetailedLoggingMiddleware: dispatch() CALLED", flush=True) # EXISTING DEBUG

        is_mcp_proxy_post_request = False
        if "POST" == request.method:
            # ADDED for debugging method and path before specific checks
            print(f"[DEBUG_PRINT] Middleware processing POST request. Path: {request.url.path}", flush=True)

            if request.url.path.startswith("/mcp/servers/") and request.url.path.endswith("/mcp"):
                 is_mcp_proxy_post_request = True
                 print(f"[DEBUG_PRINT] Middleware: Path matched for MCP proxy: {request.url.path}", flush=True) # ADDED
            else:
                 print(f"[DEBUG_PRINT] Middleware: Path DID NOT match for MCP proxy. Path: {request.url.path}", flush=True) # ADDED

        if is_mcp_proxy_post_request:
            log_prefix = "[MAIN_PY_MIDDLEWARE_RAW_HEADERS]"
            # Original logger.info calls
            logger.info(f"{log_prefix} Received POST to suspected MCP Proxy Path: {request.url.path}, Client: {request.client}")
            logger.info(f"{log_prefix} Type of request.headers: {type(request.headers)}")

            mcp_session_direct_get_lower = request.headers.get("mcp-session-id")
            mcp_session_direct_get_camel = request.headers.get("Mcp-Session-Id")
            mcp_session_direct_get_upper = request.headers.get("MCP-SESSION-ID")

            logger.info(f"{log_prefix} request.headers.get('mcp-session-id'): {mcp_session_direct_get_lower}")
            logger.info(f"{log_prefix} request.headers.get('Mcp-Session-Id'): {mcp_session_direct_get_camel}")
            logger.info(f"{log_prefix} request.headers.get('MCP-SESSION-ID'): {mcp_session_direct_get_upper}")

            headers_list = []
            for k, v in request.headers.items():
                headers_list.append((k,v))
            logger.info(f"{log_prefix} All headers via request.headers.items(): {headers_list}")

            # ADDED: Fallback print for headers if logger.info is not working as expected in this context
            print(f"{log_prefix} (PRINT) Received POST to suspected MCP Proxy Path: {request.url.path}, Client: {request.client}", flush=True)
            print(f"{log_prefix} (PRINT) Type of request.headers: {type(request.headers)}", flush=True)
            print(f"{log_prefix} (PRINT) request.headers.get('mcp-session-id'): {mcp_session_direct_get_lower}", flush=True)
            print(f"{log_prefix} (PRINT) request.headers.get('Mcp-Session-Id'): {mcp_session_direct_get_camel}", flush=True)
            print(f"{log_prefix} (PRINT) request.headers.get('MCP-SESSION-ID'): {mcp_session_direct_get_upper}", flush=True)
            print(f"{log_prefix} (PRINT) All headers via request.headers.items(): {headers_list}", flush=True)

        response = await call_next(request)
        return response
# --- END DetailedLoggingMiddleware ---

# 添加中间件
# 注意：中间件的顺序很重要。这个日志中间件应该尽可能靠前。
app.add_middleware(DetailedLoggingMiddleware) # Add our detailed logging middleware first

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 添加租户上下文中间件
app.add_middleware(TenantContextMiddleware)

# 添加操作日志中间件
app.add_middleware(OperationLogMiddleware)

# 添加自定义中间件 (示例，根据您的实际情况调整)
# app.add_middleware(TenantMiddleware)
# app.add_middleware(RbacMiddleware)

# 注册路由
app.include_router(api_router, prefix="/api")

# 注册路由
app.include_router(mcp_router, prefix="/mcp")

@app.get("/api/v1/health")
async def health_check():
    """
    健康检查接口
    """
    return {"status": "ok", "version": "1.0.0"}


@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """
    HTTP异常处理器
    """
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "code": exc.status_code,
            "message": exc.detail,
        },
    )


@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """
    通用异常处理器
    """
    logger.error(f"未处理的异常: {exc}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={
            "code": 500,
            "message": "服务器内部错误",
        },
    )


if __name__ == "__main__":
    logger.info("[main.py __main__] running")
    uvicorn.run(
        "main:app", # Uvicorn will look for an 'app' object in a module named 'main'
        host="0.0.0.0",
        port=8000,
        reload=settings.DEBUG,
        log_level="info"  # Explicitly set Uvicorn's log level
    )


