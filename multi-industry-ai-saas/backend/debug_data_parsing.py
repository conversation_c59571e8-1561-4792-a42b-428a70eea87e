#!/usr/bin/env python3
"""
数据解析调试脚本
专门分析AI返回的原始数据和解析逻辑问题
"""

import asyncio
import uuid
import logging
import json
from typing import Dict, Any

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 测试配置
TEST_PROJECT_ID = "66e74880-0f4c-4b56-bcae-12691bdbb59e"
TEST_USER_ID = "01932a18-8a66-7f20-8e37-f39b29bef0a1"
TEST_FILE_ID = "d09595ab-5c4b-4231-bfd0-dc30e697d1b3"

async def debug_ai_raw_response():
    """调试AI原始响应数据"""
    
    print("=" * 80)
    print("🔍 AI原始响应数据调试")
    print("=" * 80)
    
    try:
        from services.enhanced_table_processing_service import EnhancedTableProcessingService
        from schemas.table_processing import TableProcessingRequest
        from models.storage import StorageFile
        from db.database import AsyncSessionLocal
        from uuid import UUID
        
        async with AsyncSessionLocal() as db:
            # 获取文件信息
            file_upload = await db.get(StorageFile, UUID(TEST_FILE_ID))
            if not file_upload:
                raise ValueError(f"测试文件未找到: {TEST_FILE_ID}")
            
            # 创建处理请求
            request = TableProcessingRequest(
                file_id=UUID(TEST_FILE_ID),
                user_id=UUID(TEST_USER_ID),
                project_id=UUID(TEST_PROJECT_ID)
            )
            
            service = EnhancedTableProcessingService()
            
            # 步骤1: 测试列标题识别的原始响应
            print("\n📋 步骤1: 列标题识别原始响应")
            column_headers = await service._extract_column_headers(request, file_upload)
            
            print(f"✅ 列标题识别结果:")
            print(f"   - 商品列: {column_headers.get('product_columns', [])}")
            print(f"   - 门店列数量: {len(column_headers.get('store_columns', []))}")
            print(f"   - 门店列: {column_headers.get('store_columns', [])}")
            print(f"   - 置信度: {column_headers.get('confidence', 'unknown')}")
            
            # 步骤2: 测试单个门店的数据提取
            store_columns = column_headers.get('store_columns', [])
            if '董山' in store_columns:
                print(f"\n🏪 步骤2: 董山门店单独数据提取测试")
                
                # 只提取董山门店的数据
                dongshan_batch = ['董山']
                
                # 先获取商品信息
                products_data = await service._extract_products_info(
                    request, file_upload, column_headers.get('product_columns', [])
                )
                
                print(f"✅ 商品信息提取: {len(products_data)} 个商品")
                
                # 提取董山门店数据
                dongshan_data = await service._extract_store_batch_data(
                    request, file_upload, products_data, dongshan_batch
                )
                
                print(f"✅ 董山门店原始AI返回数据:")
                print(json.dumps(dongshan_data, ensure_ascii=False, indent=2))
                
                # 分析董山门店数据
                if '董山' in dongshan_data:
                    dongshan_items = dongshan_data['董山']
                    print(f"\n📊 董山门店数据分析:")
                    print(f"   - 商品数量: {len(dongshan_items)}")
                    
                    # 检查前5个商品的数据
                    print(f"   - 前5个商品详情:")
                    for i, item in enumerate(dongshan_items[:5]):
                        product_name = item.get('product_name', '')
                        quantity = item.get('quantity', 0)
                        specification = item.get('specification', '')
                        unit_price = item.get('unit_price', 0)
                        
                        print(f"     {i+1}. {product_name}")
                        print(f"        - 数量: {quantity}")
                        print(f"        - 规格: {specification}")
                        print(f"        - 单价: {unit_price}")
                        
                        # 判断是否是合计数据
                        if quantity > 50:
                            print(f"        ⚠️  疑似合计数据（数量过大）")
                        elif quantity == 0:
                            print(f"        ℹ️  数量为0（正常）")
                        else:
                            print(f"        ✅ 数量正常")
                
                # 步骤3: 对比多门店批次提取
                print(f"\n🏪 步骤3: 多门店批次提取对比")
                
                # 提取包含董山的批次
                dongshan_batch_multi = ['董山', '张斌桥', '宋诏桥']
                
                multi_data = await service._extract_store_batch_data(
                    request, file_upload, products_data, dongshan_batch_multi
                )
                
                print(f"✅ 多门店批次原始AI返回数据:")
                print(json.dumps(multi_data, ensure_ascii=False, indent=2))
                
                # 对比董山数据
                if '董山' in multi_data:
                    multi_dongshan = multi_data['董山']
                    single_dongshan = dongshan_data.get('董山', [])
                    
                    print(f"\n📈 董山数据对比:")
                    print(f"   - 单独提取商品数: {len(single_dongshan)}")
                    print(f"   - 批次提取商品数: {len(multi_dongshan)}")
                    
                    # 对比关键商品数量
                    key_products = ['东魁杨梅A果', '云南杨梅']
                    for product in key_products:
                        single_qty = next((item.get('quantity', 0) for item in single_dongshan if item.get('product_name') == product), 0)
                        multi_qty = next((item.get('quantity', 0) for item in multi_dongshan if item.get('product_name') == product), 0)
                        
                        print(f"   - {product}:")
                        print(f"     单独提取: {single_qty}")
                        print(f"     批次提取: {multi_qty}")
                        print(f"     是否一致: {'✅' if single_qty == multi_qty else '❌'}")
            
            else:
                print(f"❌ 董山门店未在识别的门店列表中")
                
    except Exception as e:
        print(f"❌ 调试失败: {str(e)}")
        import traceback
        traceback.print_exc()

async def debug_token_limit_issue():
    """调试Token限制问题"""
    
    print("\n" + "=" * 80)
    print("🔢 Token限制问题调试")
    print("=" * 80)
    
    try:
        from services.enhanced_table_processing_service import EnhancedTableProcessingService
        from schemas.table_processing import TableProcessingRequest
        from models.storage import StorageFile
        from db.database import AsyncSessionLocal
        from uuid import UUID
        
        async with AsyncSessionLocal() as db:
            file_upload = await db.get(StorageFile, UUID(TEST_FILE_ID))
            request = TableProcessingRequest(
                file_id=UUID(TEST_FILE_ID),
                user_id=UUID(TEST_USER_ID),
                project_id=UUID(TEST_PROJECT_ID)
            )
            
            service = EnhancedTableProcessingService()
            column_headers = await service._extract_column_headers(request, file_upload)
            store_columns = column_headers.get('store_columns', [])
            
            print(f"📊 门店数量分析:")
            print(f"   - 识别到的门店总数: {len(store_columns)}")
            print(f"   - 门店列表: {store_columns}")
            
            # 测试不同批次大小的效果
            batch_sizes = [3, 6, 10]
            
            for batch_size in batch_sizes:
                print(f"\n🔄 测试批次大小: {batch_size}")
                
                store_batches = [store_columns[i:i+batch_size] for i in range(0, len(store_columns), batch_size)]
                print(f"   - 分为 {len(store_batches)} 个批次")
                
                # 测试第一个批次
                if store_batches:
                    first_batch = store_batches[0]
                    print(f"   - 第一批门店: {first_batch}")
                    
                    # 模拟提取（不实际调用AI）
                    estimated_tokens = len(first_batch) * 500  # 估算每个门店需要500 tokens
                    print(f"   - 估算输出tokens: {estimated_tokens}")
                    
                    if estimated_tokens > 8000:
                        print(f"   - ⚠️  可能超出token限制")
                    else:
                        print(f"   - ✅ token数量合理")
            
            # 推荐最佳批次大小
            optimal_batch_size = min(3, len(store_columns))
            print(f"\n💡 推荐批次大小: {optimal_batch_size}")
            print(f"   - 理由: 确保每批次输出不超过token限制")
            print(f"   - 总批次数: {(len(store_columns) + optimal_batch_size - 1) // optimal_batch_size}")
            
    except Exception as e:
        print(f"❌ Token调试失败: {str(e)}")

async def main():
    """主调试函数"""
    
    print("🚀 开始数据解析调试")
    
    # 调试1: AI原始响应数据
    await debug_ai_raw_response()
    
    # 调试2: Token限制问题
    await debug_token_limit_issue()
    
    print("\n" + "=" * 80)
    print("📋 调试总结")
    print("=" * 80)
    print("💡 关键发现:")
    print("   1. 检查AI返回的原始数据是否正确")
    print("   2. 分析单门店vs多门店提取的差异")
    print("   3. 验证token限制对数据完整性的影响")
    print("   4. 确定最佳批次大小")

if __name__ == "__main__":
    asyncio.run(main()) 