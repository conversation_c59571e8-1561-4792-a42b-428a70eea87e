#!/usr/bin/env python3
"""
董山门店数据准确性测试
验证AI是否正确提取董山门店的分拨数据，而不是合计列数据
"""

import asyncio
import uuid
import logging
from typing import Dict, Any

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 测试配置
TEST_PROJECT_ID = "66e74880-0f4c-4b56-bcae-12691bdbb59e"
TEST_USER_ID = "01932a18-8a66-7f20-8e37-f39b29bef0a1"
TEST_FILE_ID = "d09595ab-5c4b-4231-bfd0-dc30e697d1b3"

# 根据原始表格，董山门店的预期数据（从图片中手动读取）
EXPECTED_DONGSHAN_DATA = {
    "东魁杨梅A果": 2,  # 不是10
    "云南杨梅": 6,     # 不是173
    "产地杨梅A果": 0,  # 应该不包含
    "本地杨梅A果": 0,  # 应该不包含
    # 其他商品根据实际表格数据...
}

async def test_dongshan_store_accuracy():
    """测试董山门店数据准确性"""
    
    print("=" * 80)
    print("🏪 董山门店数据准确性测试")
    print("=" * 80)
    
    try:
        # 导入必要的模块
        from tasks.enhanced_purchase_order_task_executor import EnhancedPurchaseOrderTaskExecutor
        from models.task import AsyncTask
        from uuid import UUID
        
        # 创建模拟任务
        mock_task = AsyncTask(
            id=str(uuid.uuid4()),
            project_id=UUID(TEST_PROJECT_ID),
            tenant_id=UUID(TEST_PROJECT_ID),
            user_id=UUID(TEST_USER_ID),
            task_type="purchase_order_ai_preview",
            task_name="董山门店准确性测试",
            input_data={
                "file_id": TEST_FILE_ID,
                "upload_type": "both",
                "processing_mode": "auto"
            }
        )
        
        # 执行任务
        print("🔄 执行AI识别...")
        executor = EnhancedPurchaseOrderTaskExecutor()
        task_result = await executor.execute_task(mock_task, test_mode=True)
        
        if task_result.get('success'):
            frontend_data = task_result['data']
            distribution_destinations = frontend_data.get('preview', {}).get('distribution_destinations', [])
            
            # 查找董山门店
            dongshan_store = None
            for dest in distribution_destinations:
                if '董山' in dest.get('target_name', ''):
                    dongshan_store = dest
                    break
            
            if dongshan_store:
                dongshan_items = dongshan_store.get('items', [])
                
                print(f"✅ 找到董山门店数据")
                print(f"📊 董山门店商品数量: {len(dongshan_items)}")
                print(f"\n📋 董山门店商品详情:")
                
                # 分析每个商品的数据
                accuracy_issues = []
                correct_items = []
                
                for item in dongshan_items:
                    product_name = item.get('product_name', '')
                    quantity = item.get('quantity', 0)
                    
                    print(f"   - {product_name}: 数量 {quantity}")
                    
                    # 检查是否是明显的合计列数据
                    if quantity > 50:  # 大于50的数量很可能是合计值
                        accuracy_issues.append({
                            'product': product_name,
                            'quantity': quantity,
                            'issue': '数量过大，疑似合计列数据'
                        })
                    elif quantity > 0:
                        correct_items.append({
                            'product': product_name,
                            'quantity': quantity
                        })
                
                # 分析结果
                print(f"\n📈 数据质量分析:")
                print(f"   - 总商品数: {len(dongshan_items)}")
                print(f"   - 正常商品数: {len(correct_items)}")
                print(f"   - 疑似错误数: {len(accuracy_issues)}")
                
                if accuracy_issues:
                    print(f"\n⚠️  发现疑似错误的数据:")
                    for issue in accuracy_issues:
                        print(f"   - {issue['product']}: {issue['quantity']} ({issue['issue']})")
                
                # 判断数据准确性
                if len(accuracy_issues) == 0:
                    print(f"\n🎉 董山门店数据准确！没有发现合计列数据混入。")
                    return True
                elif len(accuracy_issues) < len(dongshan_items) / 2:
                    print(f"\n⚠️  董山门店数据部分准确，但仍有改进空间。")
                    return False
                else:
                    print(f"\n❌ 董山门店数据不准确，大量合计列数据混入。")
                    return False
            else:
                print("❌ 未找到董山门店数据")
                return False
        else:
            print(f"❌ AI识别失败: {task_result.get('error')}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_store_column_recognition():
    """测试门店列识别准确性"""
    
    print("\n" + "=" * 80)
    print("🔍 门店列识别准确性测试")
    print("=" * 80)
    
    try:
        from services.enhanced_table_processing_service import EnhancedTableProcessingService
        from schemas.table_processing import TableProcessingRequest
        from models.storage import StorageFile
        from db.database import AsyncSessionLocal
        from uuid import UUID
        
        async with AsyncSessionLocal() as db:
            # 获取文件信息
            file_upload = await db.get(StorageFile, UUID(TEST_FILE_ID))
            if not file_upload:
                raise ValueError(f"测试文件未找到: {TEST_FILE_ID}")
            
            # 创建处理请求
            request = TableProcessingRequest(
                file_id=UUID(TEST_FILE_ID),
                user_id=UUID(TEST_USER_ID),
                project_id=UUID(TEST_PROJECT_ID)
            )
            
            # 测试列标题识别
            service = EnhancedTableProcessingService()
            column_headers = await service._extract_column_headers(request, file_upload)
            
            if column_headers:
                store_columns = column_headers.get('store_columns', [])
                product_columns = column_headers.get('product_columns', [])
                excluded_columns = column_headers.get('excluded_columns', [])
                
                print(f"✅ 列标题识别成功")
                print(f"📊 识别结果:")
                print(f"   - 商品信息列: {product_columns}")
                print(f"   - 门店列数量: {len(store_columns)}")
                print(f"   - 排除列: {excluded_columns}")
                
                print(f"\n🏪 识别到的门店列:")
                for i, store in enumerate(store_columns, 1):
                    print(f"   {i}. {store}")
                
                # 检查是否包含董山
                if '董山' in store_columns:
                    print(f"\n✅ 董山门店已正确识别")
                else:
                    print(f"\n❌ 董山门店未被识别")
                
                # 检查是否错误包含合计列
                problematic_stores = [s for s in store_columns if any(keyword in s for keyword in ['合计', '小计', '总计', '汇总'])]
                if problematic_stores:
                    print(f"\n⚠️  发现可能的统计列被误识别为门店: {problematic_stores}")
                else:
                    print(f"\n✅ 没有统计列被误识别为门店")
                
                return len(store_columns) > 10 and '董山' in store_columns
            else:
                print("❌ 列标题识别失败")
                return False
                
    except Exception as e:
        print(f"❌ 门店列识别测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主测试函数"""
    
    print("🚀 开始董山门店数据准确性测试")
    
    # 测试1: 门店列识别
    column_recognition_ok = await test_store_column_recognition()
    
    # 测试2: 董山门店数据准确性
    dongshan_accuracy_ok = await test_dongshan_store_accuracy()
    
    # 总结
    print("\n" + "=" * 80)
    print("📋 董山门店测试结果总结")
    print("=" * 80)
    
    print(f"✅ 门店列识别: {'通过' if column_recognition_ok else '失败'}")
    print(f"✅ 董山数据准确性: {'通过' if dongshan_accuracy_ok else '失败'}")
    
    if column_recognition_ok and dongshan_accuracy_ok:
        print("\n🎉 董山门店数据完全准确！AI正确区分了门店列和合计列。")
        print("\n💡 关键改进:")
        print("   - 明确区分门店列和合计列")
        print("   - 只提取有实际分拨数量的商品")
        print("   - 避免合计列数据混入")
        return True
    else:
        print("\n⚠️  董山门店数据仍需优化。")
        
        if not column_recognition_ok:
            print("   - 门店列识别需要改进")
        if not dongshan_accuracy_ok:
            print("   - 数据提取逻辑需要优化")
        
        return False

if __name__ == "__main__":
    asyncio.run(main()) 