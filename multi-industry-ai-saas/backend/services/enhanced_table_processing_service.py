#!/usr/bin/env python3
"""
Enhanced Table Processing Service
基于最佳实践重新设计的表格处理服务
"""

import asyncio
import json
import logging
import re
from typing import Dict, Any, List, Optional, Tuple
from uuid import UUID

try:
    import json_repair
    HAS_JSON_REPAIR = True
except ImportError:
    HAS_JSON_REPAIR = False
    print("Warning: json_repair not available, using fallback JSON parsing")

from sqlalchemy.ext.asyncio import AsyncSession

from db.database import AsyncSessionLocal
from models.storage import StorageFile
from services.ai.vision_service import AIVisionService
from schemas.table_processing import TableProcessingRequest

logger = logging.getLogger(__name__)

class EnhancedTableProcessingService:
    """增强的表格处理服务，基于最佳实践"""
    
    def __init__(self):
        self.vision_service = AIVisionService()
        
    async def process_table_with_enhanced_ai(
        self,
        request: TableProcessingRequest,
        file_upload: StorageFile
    ) -> Dict[str, Any]:
        """使用增强AI处理表格"""
        
        try:
            logger.info(f"开始增强AI表格处理: {request.file_id}")
            
            # 阶段1: 基础信息提取
            basic_info = await self._extract_basic_info(request, file_upload)
            logger.info(f"基础信息提取完成: {basic_info}")
            
            # 阶段2: 表格格式检测
            table_format = await self._detect_table_format(request, file_upload, basic_info)
            logger.info(f"表格格式检测: {table_format}")
            
            # 阶段3: 智能数据提取
            extracted_data = None
            if table_format.get('is_purchase_distribution'):
                logger.info("检测为采购分拨单，使用专门的提取逻辑")
                extracted_data = await self._extract_purchase_distribution_data(
                    request, file_upload, basic_info, table_format
                )
            else:
                logger.info("检测为通用表格，使用通用提取逻辑")
                extracted_data = await self._extract_generic_data(
                    request, file_upload, basic_info, table_format
                )
            
            # 处理结果
            if extracted_data and isinstance(extracted_data, dict):
                # 确保数据结构完整
                processed_data = self._ensure_data_structure(extracted_data, table_format)
                
                return {
                    'success': True,
                    'data': processed_data,
                    'metadata': {
                        'basic_info': basic_info,
                        'table_format': table_format,
                        'processing_stages': ['basic_info', 'format_detection', 'data_extraction'],
                        'extraction_method': 'purchase_distribution' if table_format.get('is_purchase_distribution') else 'generic'
                    }
                }
            else:
                logger.warning("数据提取失败，返回空结构")
                return {
                    'success': False,
                    'error': '数据提取失败或返回空数据',
                    'data': self._get_empty_data_structure(),
                    'metadata': {
                        'basic_info': basic_info,
                        'table_format': table_format,
                        'processing_stages': ['basic_info', 'format_detection', 'data_extraction_failed']
                    }
                }
                
        except Exception as e:
            logger.error(f"增强AI表格处理失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return {
                'success': False,
                'error': str(e),
                'data': self._get_empty_data_structure(),
                'metadata': {
                    'processing_stages': ['error'],
                    'error_details': str(e)
                }
            }
    
    async def _extract_basic_info(
        self, 
        request: TableProcessingRequest, 
        file_upload: StorageFile
    ) -> Dict[str, Any]:
        """提取基础信息"""
        
        try:
            # 构建基础信息提取提示词
            prompt = """
            请分析这个表格图像的基础信息，返回JSON格式：
            {
                "table_type": "表格类型(purchase_distribution/inventory/purchase_order/other)",
                "row_count": "大概行数",
                "column_count": "大概列数",
                "has_headers": true/false,
                "language": "主要语言",
                "data_density": "数据密度(high/medium/low)",
                "image_quality": "图像质量(excellent/good/fair/poor)",
                "special_features": ["特殊特征列表"]
            }
            """
            
            # 调用AI视觉服务
            async with AsyncSessionLocal() as db:
                result = await self.vision_service.analyze_image(
                    db=db,
                    project_id=file_upload.project_id,
                    user_id=file_upload.uploaded_by,
                    image_data=self._load_image_as_base64(file_upload),
                    prompt=prompt,
                    temperature=0.1,
                    max_tokens=1024
                )
            
            # 解析结果
            if result.get('success') and result.get('content'):
                try:
                    if HAS_JSON_REPAIR:
                        basic_info = json_repair.loads(result['content'])
                    else:
                        basic_info = json.loads(result['content'])
                    return basic_info
                except:
                    pass
            
            # 返回默认值
            return {
                'table_type': 'unknown',
                'row_count': 'unknown',
                'column_count': 'unknown',
                'has_headers': True,
                'language': 'chinese',
                'data_density': 'medium',
                'image_quality': 'fair',
                'special_features': []
            }
            
        except Exception as e:
            logger.error(f"基础信息提取失败: {str(e)}")
            return {
                'table_type': 'unknown',
                'row_count': 'unknown',
                'column_count': 'unknown',
                'has_headers': True,
                'language': 'chinese',
                'data_density': 'medium',
                'image_quality': 'fair',
                'special_features': []
            }
    
    async def _detect_table_format(
        self, 
        request: TableProcessingRequest, 
        file_upload: StorageFile,
        basic_info: Dict[str, Any]
    ) -> Dict[str, Any]:
        """检测表格格式"""
        
        try:
            prompt = """
            请仔细分析这个表格的格式特征，特别关注是否为采购分拨单。
            
            采购分拨单的典型特征：
            1. 第一列通常是商品名称
            2. 前几列包含商品信息（名称、规格、单位、单价、合计、分类、品牌等）
            3. 后面的列是门店名称（如：万达、南塘、江东、江北等）
            4. 表格数据显示各商品在不同门店的分拨数量
            
            返回JSON格式：
            {
                "is_purchase_distribution": true/false,
                "is_purchase_order": true/false,
                "has_store_columns": true/false,
                "has_product_rows": true/false,
                "store_column_pattern": "horizontal/vertical/none",
                "product_info_columns": ["商品信息列名列表"],
                "distribution_columns": ["分拨列名列表"],
                "key_identifiers": ["关键标识符"],
                "complexity_level": "simple/medium/complex"
            }
            
            请特别注意：如果看到类似"万达"、"南塘"、"江东"等门店名称作为列标题，
            并且前面有商品信息列，那么很可能是采购分拨单。
            """
            
            from db.database import AsyncSessionLocal
            async with AsyncSessionLocal() as db:
                result = await self.vision_service.analyze_image(
                    db=db,
                    project_id=file_upload.project_id,
                    user_id=file_upload.uploaded_by,
                    image_data=self._load_image_as_base64(file_upload),
                    prompt=prompt,
                    temperature=0.1,
                    max_tokens=1024
                )
            
            logger.info(f"格式检测AI返回结果: {result}")
            
            if result and 'choices' in result:
                # 处理AI API的原始响应格式
                choices = result.get('choices', [])
                if choices and len(choices) > 0:
                    content = choices[0].get('message', {}).get('content', '')
                    logger.info(f"格式检测AI返回内容: {content}")
                    
                    try:
                        # 清理JSON内容
                        cleaned_content = self._clean_json_response(content)
                        
                        # 检查是否只有标记没有内容
                        if cleaned_content.strip() in ['', '```json', '```']:
                            logger.warning("格式检测返回的内容为空或只有标记，假设为采购分拨单")
                            return self._get_purchase_distribution_format()
                        
                        format_info = json_repair.loads(cleaned_content)
                        logger.info(f"格式检测解析成功: {format_info}")
                        
                        # 确保返回正确的格式信息，修复解析错误
                        corrected_format = {
                            'is_purchase_distribution': format_info.get('is_purchase_distribution', False),
                            'is_purchase_order': format_info.get('is_purchase_order', False),
                            'has_store_columns': format_info.get('has_store_columns', False),
                            'has_product_rows': format_info.get('has_product_rows', True),
                            'store_column_pattern': format_info.get('store_column_pattern', 'none'),
                            'product_info_columns': format_info.get('product_info_columns', []),
                            'distribution_columns': format_info.get('distribution_columns', []),
                            'key_identifiers': format_info.get('key_identifiers', []),
                            'complexity_level': format_info.get('complexity_level', 'medium')
                        }
                        
                        logger.info(f"修正后的格式信息: {corrected_format}")
                        return corrected_format
                        
                    except Exception as parse_error:
                        logger.error(f"格式检测JSON解析失败: {parse_error}")
                        logger.error(f"原始内容: {content}")
                        logger.warning("格式检测失败，假设为采购分拨单")
                        return self._get_purchase_distribution_format()
            
            # 返回默认格式信息
            return {
                'is_purchase_distribution': False,
                'is_purchase_order': False,
                'has_store_columns': False,
                'has_product_rows': True,
                'store_column_pattern': 'none',
                'product_info_columns': [],
                'distribution_columns': [],
                'key_identifiers': [],
                'complexity_level': 'medium'
            }
            
        except Exception as e:
            logger.error(f"格式检测失败: {str(e)}")
            return {
                'is_purchase_distribution': False,
                'is_purchase_order': False,
                'has_store_columns': False,
                'has_product_rows': True,
                'store_column_pattern': 'none',
                'product_info_columns': [],
                'distribution_columns': [],
                'key_identifiers': [],
                'complexity_level': 'medium'
            }
    
    async def _extract_purchase_distribution_data(
        self,
        request: TableProcessingRequest,
        file_upload: StorageFile,
        basic_info: Dict[str, Any],
        table_format: Dict[str, Any]
    ) -> Dict[str, Any]:
        """提取采购分拨单数据 - 分阶段识别策略"""
        
        try:
            # 阶段1: 先识别所有列标题（门店名称）
            logger.info("阶段1: 识别表格列标题和门店名称")
            column_headers = await self._extract_column_headers(request, file_upload)
            
            # 阶段2: 基于识别的列标题提取完整数据
            logger.info(f"阶段2: 基于{len(column_headers.get('store_columns', []))}个门店列提取数据")
            distribution_data = await self._extract_distribution_with_headers(
                request, file_upload, column_headers
            )
            
            return distribution_data
            
        except Exception as e:
            logger.error(f"采购分拨数据提取失败: {str(e)}")
            return None
    
    async def _extract_column_headers(
        self,
        request: TableProcessingRequest,
        file_upload: StorageFile
    ) -> Dict[str, Any]:
        """专门识别表格的列标题"""
        
        try:
            prompt = """
            请识别这个采购分拨表格的所有列标题。
            
            表格结构：
            - 左侧：商品信息列（商品名称、规格、单位、单价等）
            - 右侧：门店分拨列（具体门店名称，如：董山、张斌桥、宋诏桥等）
            
            注意：排除"合计"、"小计"、"总计"等统计列，只识别具体门店名称。
            
            返回JSON格式：
            {
                "product_columns": ["商品名称", "规格", "单位", "单价"],
                "store_columns": ["董山", "张斌桥", "宋诏桥", "..."],
                "excluded_columns": ["合计", "小计"],
                "total_columns": 总列数,
                "confidence": "high/medium/low"
            }
            """
            
            async with AsyncSessionLocal() as db:
                result = await self.vision_service.analyze_image(
                    db=db,
                    project_id=file_upload.project_id,
                    user_id=file_upload.uploaded_by,
                    image_data=self._load_image_as_base64(file_upload),
                    prompt=prompt,
                    temperature=0.01,
                    max_tokens=8192
                )
            
            if result and 'choices' in result:
                choices = result.get('choices', [])
                if choices and len(choices) > 0:
                    content = choices[0].get('message', {}).get('content', '')
                    logger.info(f"列标题识别AI返回内容: {content}")
                    
                    try:
                        cleaned_content = self._clean_json_response(content)
                        if HAS_JSON_REPAIR:
                            headers_data = json_repair.loads(cleaned_content)
                        else:
                            headers_data = json.loads(cleaned_content)
                        
                        store_columns = headers_data.get('store_columns', [])
                        logger.info(f"识别到 {len(store_columns)} 个门店列: {store_columns}")
                        
                        if len(store_columns) < 15:
                            logger.warning(f"门店列数量可能不足: {len(store_columns)}")
                        
                        return headers_data
                        
                    except Exception as parse_error:
                        logger.error(f"列标题JSON解析失败: {parse_error}")
            
            # AI识别失败，返回空值
            logger.error("列标题识别失败，无法解析门店列")
            return {
                "product_columns": [],
                "store_columns": [],
                "total_columns": 0,
                "confidence": "failed"
            }
            
        except Exception as e:
            logger.error(f"列标题识别失败: {str(e)}")
            return {
                "product_columns": [],
                "store_columns": [],
                "total_columns": 0,
                "confidence": "failed"
            }
    
    async def _extract_distribution_with_headers(
        self,
        request: TableProcessingRequest,
        file_upload: StorageFile,
        column_headers: Dict[str, Any]
    ) -> Dict[str, Any]:
        """基于已识别的列标题提取分拨数据 - 分批处理策略"""
        
        try:
            store_columns = column_headers.get('store_columns', [])
            product_columns = column_headers.get('product_columns', [])
            
            logger.info(f"开始分批处理 {len(store_columns)} 个门店")
            
            # 分批处理策略：每批处理5个门店
            batch_size = 5
            store_batches = [store_columns[i:i+batch_size] for i in range(0, len(store_columns), batch_size)]
            
            logger.info(f"分为 {len(store_batches)} 个批次，每批最多 {batch_size} 个门店")
            
            # 首先提取商品信息（只需要一次）
            products_data = await self._extract_products_info(request, file_upload, product_columns)
            
            # 分批提取各门店数据
            all_distribution = {}
            
            for batch_index, store_batch in enumerate(store_batches):
                logger.info(f"处理第 {batch_index + 1}/{len(store_batches)} 批门店: {store_batch}")
                
                batch_distribution = await self._extract_store_batch_data(
                    request, file_upload, products_data, store_batch
                )
                
                if batch_distribution:
                    all_distribution.update(batch_distribution)
                    logger.info(f"第 {batch_index + 1} 批完成，累计门店数: {len(all_distribution)}")
                else:
                    logger.warning(f"第 {batch_index + 1} 批提取失败")
            
            # 合并结果
            final_result = {
                "products": products_data,
                "distribution": all_distribution,
                "metadata": {
                    "total_stores": len(all_distribution),
                    "total_products": len(products_data),
                    "extraction_method": "batch_processing",
                    "batch_count": len(store_batches)
                }
            }
            
            logger.info(f"分批处理完成，最终商品数: {len(products_data)}, 门店数: {len(all_distribution)}")
            return self._standardize_purchase_distribution_data(final_result)
            
        except Exception as e:
            logger.error(f"分批处理失败: {str(e)}")
            return None
    
    async def _extract_products_info(
        self,
        request: TableProcessingRequest,
        file_upload: StorageFile,
        product_columns: List[str]
    ) -> List[Dict[str, Any]]:
        """提取商品基本信息并推荐分类"""
        
        try:
            # 获取现有的分类和品牌信息
            async with AsyncSessionLocal() as db:
                from services.product_service import ProductService
                categories_brands = await ProductService.get_existing_categories_and_brands(
                    db, file_upload.project_id
                )
            
            existing_categories = categories_brands.get('categories', [])
            existing_brands = categories_brands.get('brands', [])
            
            # 构建分类提示信息
            category_hint = ""
            if existing_categories:
                category_hint = f"\n【现有分类参考】\n系统中已有以下分类：{', '.join(existing_categories[:10])}"
                if len(existing_categories) > 10:
                    category_hint += f"等{len(existing_categories)}个分类"
            
            brand_hint = ""
            if existing_brands:
                brand_hint = f"\n【现有品牌参考】\n系统中已有以下品牌：{', '.join(existing_brands[:10])}"
                if len(existing_brands) > 10:
                    brand_hint += f"等{len(existing_brands)}个品牌"
            
            prompt = f"""
            请提取表格中所有商品的基本信息。
            
            观察表格，提取每行商品的：商品名称、规格、单位、单价，并推荐分类和品牌。
            {category_hint}
            {brand_hint}
            
            返回JSON格式：
            {{
                "products": [
                    {{
                        "name": "商品名称",
                        "specification": "规格",
                        "unit": "单位",
                        "unit_price": 单价,
                        "suggested_category": "推荐分类",
                        "suggested_brand": "推荐品牌"
                    }}
                ]
            }}
            
            请提取所有商品，保持原始数据准确性。
            """
            
            async with AsyncSessionLocal() as db:
                result = await self.vision_service.analyze_image(
                    db=db,
                    project_id=file_upload.project_id,
                    user_id=file_upload.uploaded_by,
                    image_data=self._load_image_as_base64(file_upload),
                    prompt=prompt,
                    temperature=0.01,
                    max_tokens=8192
                )
            
            if result and 'choices' in result:
                choices = result.get('choices', [])
                if choices and len(choices) > 0:
                    content = choices[0].get('message', {}).get('content', '')
                    
                    try:
                        cleaned_content = self._clean_json_response(content)
                        if HAS_JSON_REPAIR:
                            data = json_repair.loads(cleaned_content)
                        else:
                            data = json.loads(cleaned_content)
                        
                        products = data.get('products', [])
                        logger.info(f"商品信息提取成功，共 {len(products)} 个商品")
                        return products
                        
                    except Exception as parse_error:
                        logger.error(f"商品信息JSON解析失败: {parse_error}")
            
            return []
            
        except Exception as e:
            logger.error(f"商品信息提取失败: {str(e)}")
            return []
    
    async def _extract_store_batch_data(
        self,
        request: TableProcessingRequest,
        file_upload: StorageFile,
        products_data: List[Dict[str, Any]],
        store_batch: List[str]
    ) -> Dict[str, Any]:
        """提取指定门店批次的分拨数据"""
        
        try:
            store_list = '", "'.join(store_batch)
            
            prompt = f"""
            请提取表格中这些门店的商品分拨数据：{store_list}
            
            重要说明：
            1. 仔细找到每个门店对应的列（不是合计列）
            2. 只读取该门店列的具体数量，不要读取合计列的数量
            3. 如果某个门店对某商品的分拨数量为0或空白，则不要包含该商品
            4. 确保读取的是门店列的数值，而不是合计、小计等统计列的数值
            
            返回JSON格式：
            {{
                "门店名称": [
                    {{
                        "product_name": "商品名称",
                        "specification": "规格",
                        "unit": "单位", 
                        "quantity": 该门店的实际分拨数量,
                        "unit_price": 单价,
                        "subtotal": 规格×数量×单价,
                        "suggested_category": "推荐的商品分类",
                        "suggested_brand": "推荐的商品品牌（可选）"
                    }}
                ]
            }}
            
            关键：只提取该门店有实际分拨数量（>0）的商品，忽略数量为0的商品。
            """
            
            async with AsyncSessionLocal() as db:
                result = await self.vision_service.analyze_image(
                    db=db,
                    project_id=file_upload.project_id,
                    user_id=file_upload.uploaded_by,
                    image_data=self._load_image_as_base64(file_upload),
                    prompt=prompt,
                    temperature=0.01,
                    max_tokens=16384
                )
            
            if result and 'choices' in result:
                choices = result.get('choices', [])
                if choices and len(choices) > 0:
                    content = choices[0].get('message', {}).get('content', '')
                    
                    try:
                        cleaned_content = self._clean_json_response(content)
                        if HAS_JSON_REPAIR:
                            batch_data = json_repair.loads(cleaned_content)
                        else:
                            batch_data = json.loads(cleaned_content)
                        
                        logger.info(f"门店批次数据提取成功，获得 {len(batch_data)} 个门店数据")
                        return batch_data
                        
                    except Exception as parse_error:
                        logger.error(f"门店批次JSON解析失败: {parse_error}")
            
            return {}
            
        except Exception as e:
            logger.error(f"门店批次数据提取失败: {str(e)}")
            return {}
    
    async def _extract_generic_data(
        self,
        request: TableProcessingRequest,
        file_upload: StorageFile,
        basic_info: Dict[str, Any],
        table_format: Dict[str, Any]
    ) -> Dict[str, Any]:
        """提取通用表格数据"""
        
        try:
            prompt = """
            请提取这个表格中的所有数据，返回JSON格式：
            {
                "headers": ["列标题列表"],
                "rows": [
                    {"列1": "值1", "列2": "值2", ...}
                ],
                "summary": {
                    "total_rows": 总行数,
                    "total_columns": 总列数,
                    "data_types": {"列名": "数据类型"}
                }
            }
            """
            
            from db.database import AsyncSessionLocal
            async with AsyncSessionLocal() as db:
                result = await self.vision_service.analyze_image(
                    db=db,
                    project_id=file_upload.project_id,
                    user_id=file_upload.uploaded_by,
                    image_data=self._load_image_as_base64(file_upload),
                    prompt=prompt,
                    temperature=0.1,
                    max_tokens=8192
                )
            
            logger.info(f"通用数据提取AI返回结果: {result}")
            
            if result and 'choices' in result:
                # 处理AI API的原始响应格式
                choices = result.get('choices', [])
                if choices and len(choices) > 0:
                    content = choices[0].get('message', {}).get('content', '')
                    logger.info(f"AI返回内容长度: {len(content)}")
                    logger.info(f"AI返回内容前500字符: {content[:500]}...")
                    
                    try:
                        # 清理和修复JSON
                        cleaned_content = self._clean_json_response(content)
                        logger.info(f"清理后内容长度: {len(cleaned_content)}")
                        
                        # 如果内容被截断，尝试修复
                        if len(content) >= 8000 and not cleaned_content.rstrip().endswith('}'):
                            logger.warning("检测到可能的JSON截断，尝试修复...")
                            cleaned_content = self._repair_truncated_json(cleaned_content)
                        
                        data = json_repair.loads(cleaned_content)
                        logger.info(f"JSON解析成功，数据类型: {type(data)}")
                        
                        # 转换为采购分拨格式
                        if 'headers' in data and 'rows' in data:
                            converted_data = self._convert_generic_to_distribution_format(data)
                            logger.info(f"转换为分拨格式成功，商品数: {len(converted_data.get('products', []))}, 门店数: {len(converted_data.get('distribution', {}))}")
                            return converted_data
                        
                        return data
                        
                    except Exception as parse_error:
                        logger.error(f"JSON解析失败: {parse_error}")
                        logger.error(f"原始内容长度: {len(content)}")
                        logger.error(f"清理后内容: {cleaned_content[:1000]}...")
                else:
                    logger.error("AI响应中没有有效的choices内容")
            else:
                logger.error(f"AI调用失败或响应格式错误: {result}")
            
            return None
            
        except Exception as e:
            logger.error(f"通用数据提取失败: {str(e)}")
            return None
    
    def _load_image_as_base64(self, file_upload: StorageFile) -> str:
        """加载图片为base64格式"""
        
        import base64
        import os
        
        # 构建完整路径
        base_upload_path = "/app/uploads"
        full_path = base_upload_path + file_upload.storage_path
        
        if os.path.exists(full_path):
            with open(full_path, "rb") as image_file:
                return base64.b64encode(image_file.read()).decode('utf-8')
        else:
            raise FileNotFoundError(f"图片文件不存在: {full_path}")
    
    def _ensure_data_structure(self, data: Dict[str, Any], table_format: Dict[str, Any]) -> Dict[str, Any]:
        """确保数据结构完整性"""
        
        if table_format.get('is_purchase_distribution'):
            # 采购分拨单数据结构
            return {
                'products': data.get('products', []),
                'distribution': data.get('distribution', {}),
                'product_count': len(data.get('products', [])),
                'store_count': len(data.get('distribution', {})),
                'summary': {
                    'total_products': len(data.get('products', [])),
                    'total_stores': len(data.get('distribution', {})),
                    'data_completeness': self._calculate_completeness(data)
                }
            }
        else:
            # 通用表格数据结构
            return {
                'headers': data.get('headers', []),
                'rows': data.get('rows', []),
                'summary': data.get('summary', {
                    'total_rows': len(data.get('rows', [])),
                    'total_columns': len(data.get('headers', [])),
                    'data_types': {}
                })
            }
    
    def _get_empty_data_structure(self) -> Dict[str, Any]:
        """获取空的数据结构"""
        return {
            'products': [],
            'distribution': {},
            'product_count': 0,
            'store_count': 0,
            'summary': {
                'total_products': 0,
                'total_stores': 0,
                'data_completeness': 0.0
            }
        }
    
    def _calculate_completeness(self, data: Dict[str, Any]) -> float:
        """计算数据完整性"""
        try:
            products = data.get('products', [])
            distribution = data.get('distribution', {})
            
            if not products or not distribution:
                return 0.0
            
            # 计算有规格信息的商品比例
            products_with_spec = sum(1 for p in products if p.get('specification'))
            spec_completeness = products_with_spec / len(products) if products else 0
            
            # 计算有分拨数据的门店比例
            stores_with_data = sum(1 for store_data in distribution.values() if store_data)
            store_completeness = stores_with_data / len(distribution) if distribution else 0
            
            # 综合完整性评分
            return (spec_completeness + store_completeness) / 2
            
        except Exception as e:
            logger.error(f"计算数据完整性失败: {e}")
            return 0.0
    
    def _clean_json_response(self, response: str) -> str:
        """清理AI响应，提取JSON部分"""
        
        # 移除markdown代码块
        response = re.sub(r'```json\s*', '', response)
        response = re.sub(r'```\s*$', '', response)
        
        # 查找JSON对象
        json_match = re.search(r'\{.*\}', response, re.DOTALL)
        if json_match:
            return json_match.group(0)
        
        # 如果没找到完整JSON，尝试修复
        return response.strip()
    
    def _parse_number(self, value) -> float:
        """解析数字值"""
        if isinstance(value, (int, float)):
            return float(value)
        
        if isinstance(value, str):
            # 移除非数字字符，保留小数点
            cleaned = re.sub(r'[^\d.]', '', value)
            try:
                return float(cleaned) if cleaned else 0.0
            except ValueError:
                return 0.0
        
        return 0.0
    
    def _validate_distribution_data_quality(self, data: Dict) -> bool:
        """验证分拨数据质量"""
        
        # 检查基本结构
        if not isinstance(data, dict):
            return False
        
        # 检查必要字段
        required_fields = ['products', 'stores', 'distribution_data']
        for field in required_fields:
            if field not in data:
                return False
        
        # 检查门店数量（至少应该有2个门店）
        stores = data.get('stores', [])
        distribution_data = data.get('distribution_data', {})
        
        if len(stores) < 2 or len(distribution_data) < 2:
            return False
        
        # 检查是否有真实门店名称（不是通用名称）
        real_store_count = 0
        for store in stores:
            store_name = store.get('门店名称', '')
            if not re.match(r'^门店[A-Z]$', store_name) and len(store_name) >= 2:
                real_store_count += 1
        
        return real_store_count >= 2
    
    def _repair_truncated_json(self, json_str: str) -> str:
        """修复截断的JSON"""
        
        try:
            # 尝试使用json-repair修复
            import json_repair
            repaired = json_repair.repair_json(json_str)
            return repaired
        except Exception as e:
            logger.warning(f"json-repair修复失败: {e}")
            
            # 手动修复策略
            try:
                # 找到最后一个完整的对象结束位置
                brace_count = 0
                last_complete_pos = -1
                
                for i, char in enumerate(json_str):
                    if char == '{':
                        brace_count += 1
                    elif char == '}':
                        brace_count -= 1
                        if brace_count == 0:
                            last_complete_pos = i
                
                if last_complete_pos > 0:
                    # 截取到最后一个完整对象
                    repaired = json_str[:last_complete_pos + 1]
                    logger.info(f"手动修复JSON，截取到位置: {last_complete_pos}")
                    return repaired
                else:
                    # 如果找不到完整对象，添加缺失的括号
                    missing_braces = json_str.count('{') - json_str.count('}')
                    if missing_braces > 0:
                        repaired = json_str + '}' * missing_braces
                        logger.info(f"添加了 {missing_braces} 个缺失的括号")
                        return repaired
                        
            except Exception as repair_error:
                logger.error(f"手动修复JSON失败: {repair_error}")
            
            return json_str
    
    def _convert_generic_to_distribution_format(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """将通用表格数据转换为采购分拨格式"""
        
        try:
            headers = data.get('headers', [])
            rows = data.get('rows', [])
            
            if not headers or not rows:
                return {'products': [], 'distribution': {}}
            
            # 识别商品信息列和门店列
            product_columns = ['商品名称', '单位', '规格', '单价', '合计']
            product_col_indices = []
            store_col_indices = []
            
            for i, header in enumerate(headers):
                if header in product_columns:
                    product_col_indices.append(i)
                elif header and not header in product_columns and len(header) <= 10:
                    # 可能是门店名称
                    store_col_indices.append(i)
            
            logger.info(f"识别到商品列索引: {product_col_indices}")
            logger.info(f"识别到门店列索引: {store_col_indices}")
            
            # 提取商品信息
            products = []
            distribution = {}
            
            # 初始化门店分拨数据
            for store_idx in store_col_indices:
                if store_idx < len(headers):
                    store_name = headers[store_idx]
                    distribution[store_name] = []
            
            # 处理每一行数据
            for row in rows:
                if not isinstance(row, dict):
                    continue
                
                # 提取商品信息
                product_name = ""
                specification = "1"
                unit = ""
                unit_price = 0
                
                # 从行数据中提取商品信息
                row_values = list(row.values())
                if len(row_values) > 0:
                    product_name = str(row_values[0]) if row_values[0] else ""
                if len(row_values) > 1:
                    unit = str(row_values[1]) if row_values[1] else ""
                if len(row_values) > 2:
                    specification = str(row_values[2]) if row_values[2] else "1"
                if len(row_values) > 3:
                    try:
                        unit_price = float(row_values[3]) if row_values[3] else 0
                    except (ValueError, TypeError):
                        unit_price = 0
                
                if product_name:
                    product = {
                        'name': product_name,
                        'specification': specification,
                        'unit': unit,
                        'unit_price': unit_price,
                        'notes': ''
                    }
                    products.append(product)
                    
                    # 提取门店分拨数据
                    for store_idx in store_col_indices:
                        if store_idx < len(headers) and store_idx < len(row_values):
                            store_name = headers[store_idx]
                            quantity_str = str(row_values[store_idx]) if row_values[store_idx] else "0"
                            
                            try:
                                quantity = float(quantity_str) if quantity_str and quantity_str != "" else 0
                                if quantity > 0:
                                    store_item = {
                                        'product_name': product_name,
                                        'quantity': quantity,
                                        'unit_price': unit_price,
                                        'specification': specification,
                                        'subtotal': quantity * unit_price
                                    }
                                    distribution[store_name].append(store_item)
                            except (ValueError, TypeError):
                                continue
            
            logger.info(f"转换完成: {len(products)} 个商品, {len(distribution)} 个门店")
            
            return {
                'products': products,
                'distribution': distribution,
                'product_count': len(products),
                'store_count': len(distribution)
            }
            
        except Exception as e:
            logger.error(f"转换格式失败: {e}")
            return {'products': [], 'distribution': {}}
    
    def _standardize_purchase_distribution_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """标准化采购分拨数据"""
        try:
            standardized = {
                "products": [],
                "distribution": {},
                "metadata": {
                    "total_stores": 0,
                    "total_products": 0,
                    "completeness_score": 0.0
                }
            }
            
            # 处理来自AI的metadata
            ai_metadata = data.get('metadata', {})
            
            # 标准化商品信息
            products = data.get('products', [])
            for product in products:
                standardized_product = {
                    "name": str(product.get('name', '')).strip(),
                    "specification": self._extract_specification(product.get('specification', '')),
                    "unit": str(product.get('unit', '')).strip(),
                    "unit_price": self._safe_float(product.get('unit_price', 0)),
                    "notes": str(product.get('notes', '')).strip()
                }
                if standardized_product["name"]:
                    standardized["products"].append(standardized_product)
            
            # 标准化门店分拨数据
            distribution = data.get('distribution', {})
            for store_name, store_products in distribution.items():
                if not isinstance(store_products, list):
                    continue
                    
                standardized_store_products = []
                for product in store_products:
                    standardized_product = {
                        "product_name": str(product.get('product_name', '')).strip(),
                        "quantity": self._safe_float(product.get('quantity', 0)),
                        "unit_price": self._safe_float(product.get('unit_price', 0)),
                        "specification": self._extract_specification(product.get('specification', '')),
                        "unit": str(product.get('unit', '')).strip(),  # 添加缺失的unit字段
                        "subtotal": self._safe_float(product.get('subtotal', 0))
                    }
                    if standardized_product["product_name"]:
                        standardized_store_products.append(standardized_product)
                
                if standardized_store_products:
                    clean_store_name = str(store_name).strip()
                    if clean_store_name:
                        standardized["distribution"][clean_store_name] = standardized_store_products
            
            # 更新元数据
            standardized["metadata"]["total_products"] = len(standardized["products"])
            standardized["metadata"]["total_stores"] = len(standardized["distribution"])
            
            # 记录AI提取的统计信息作为参考
            if ai_metadata:
                ai_store_count = ai_metadata.get('total_stores', 0)
                ai_product_count = ai_metadata.get('total_products', 0)
                
                logger.info(f"AI报告的门店数: {ai_store_count}, 实际解析门店数: {standardized['metadata']['total_stores']}")
                logger.info(f"AI报告的商品数: {ai_product_count}, 实际解析商品数: {standardized['metadata']['total_products']}")
                
                # 如果实际解析的门店数明显少于AI报告的数量，记录警告
                if ai_store_count > standardized['metadata']['total_stores'] * 2:
                    logger.warning(f"可能存在门店识别不完整：AI识别{ai_store_count}个门店，但只解析出{standardized['metadata']['total_stores']}个")
                    standardized["metadata"]["extraction_warning"] = f"可能遗漏门店：期望{ai_store_count}个，实际{standardized['metadata']['total_stores']}个"
            
            # 计算完整性评分
            completeness_score = self._calculate_completeness_score(standardized)
            standardized["metadata"]["completeness_score"] = completeness_score
            
            return standardized
            
        except Exception as e:
            logger.error(f"采购分拨数据标准化失败: {str(e)}")
            return {
                "products": [],
                "distribution": {},
                "metadata": {
                    "total_stores": 0,
                    "total_products": 0,
                    "completeness_score": 0.0,
                    "error": str(e)
                }
            }
    
    def _extract_specification(self, spec_value) -> str:
        """提取规格信息"""
        if not spec_value:
            return "1"
        
        spec_str = str(spec_value).strip()
        
        # 如果是"无"或空值，返回默认规格
        if spec_str in ['无', '', 'None', 'null']:
            return "1"
        
        # 清理规格字符串
        spec_str = re.sub(r'[^\d\.\w\u4e00-\u9fff]+', '', spec_str)
        
        return spec_str if spec_str else "1"
    
    def _safe_float(self, value) -> float:
        """安全转换为浮点数"""
        if isinstance(value, (int, float)):
            return float(value)
        
        if isinstance(value, str):
            # 移除非数字字符，保留小数点
            cleaned = re.sub(r'[^\d.-]', '', value)
            try:
                return float(cleaned) if cleaned else 0.0
            except ValueError:
                return 0.0
        
        return 0.0
    
    def _calculate_completeness_score(self, data: Dict[str, Any]) -> float:
        """计算数据完整性评分"""
        try:
            products = data.get('products', [])
            distribution = data.get('distribution', {})
            
            if not products or not distribution:
                return 0.0
            
            # 基础分数
            base_score = 0.5
            
            # 商品信息完整性
            product_completeness = 0
            for product in products:
                score = 0
                if product.get('name'):
                    score += 0.25
                if product.get('specification') and product['specification'] != "1":
                    score += 0.25
                if product.get('unit'):
                    score += 0.25
                if product.get('unit_price', 0) > 0:
                    score += 0.25
                product_completeness += score
            
            if products:
                product_completeness /= len(products)
            
            # 门店分拨完整性
            distribution_completeness = 0
            total_items = 0
            complete_items = 0
            
            for store_products in distribution.values():
                for item in store_products:
                    total_items += 1
                    score = 0
                    if item.get('product_name'):
                        score += 0.2
                    if item.get('quantity', 0) > 0:
                        score += 0.3
                    if item.get('unit_price', 0) > 0:
                        score += 0.3
                    if item.get('specification'):
                        score += 0.1
                    if item.get('subtotal', 0) > 0:
                        score += 0.1
                    
                    if score >= 0.8:
                        complete_items += 1
            
            if total_items > 0:
                distribution_completeness = complete_items / total_items
            
            # 门店数量评分
            store_count_score = min(len(distribution) / 10, 1.0)  # 假设理想门店数为10
            
            # 综合评分
            final_score = (
                base_score * 0.2 +
                product_completeness * 0.3 +
                distribution_completeness * 0.4 +
                store_count_score * 0.1
            )
            
            return round(final_score, 2)
            
        except Exception as e:
            logger.error(f"计算完整性评分失败: {e}")
            return 0.0
    
    def _get_purchase_distribution_format(self) -> Dict[str, Any]:
        """获取采购分拨单格式信息"""
        return {
            'is_purchase_distribution': True,
            'is_purchase_order': False,
            'has_store_columns': True,
            'has_product_rows': True,
            'store_column_pattern': 'horizontal',
            'product_info_columns': ['商品名称', '单位', '规格', '单价', '合计'],
            'distribution_columns': ['门店名称'],
            'key_identifiers': ['商品名称', '单位', '规格', '单价', '合计', '门店名称'],
            'complexity_level': 'medium'
        }

    async def _extract_batched_distribution_data(
        self,
        request: TableProcessingRequest,
        file_upload: StorageFile,
        products_data: List[Dict[str, Any]],
        store_list: List[str],
        batch_size: int
    ) -> Dict[str, Any]:
        """分批提取分拨数据"""
        
        try:
            # 分批处理门店
            store_batches = [store_list[i:i+batch_size] for i in range(0, len(store_list), batch_size)]
            logger.info(f"分为 {len(store_batches)} 个批次，每批最多 {batch_size} 个门店")
            
            all_distribution = {}
            
            for batch_index, store_batch in enumerate(store_batches):
                logger.info(f"处理第 {batch_index + 1}/{len(store_batches)} 批门店: {store_batch}")
                
                batch_distribution = await self._extract_store_batch_data(
                    request, file_upload, products_data, store_batch
                )
                
                if batch_distribution:
                    all_distribution.update(batch_distribution)
                    logger.info(f"第 {batch_index + 1} 批完成，累计门店数: {len(all_distribution)}")
                else:
                    logger.warning(f"第 {batch_index + 1} 批提取失败")
            
            logger.info(f"分批处理完成，最终门店数: {len(all_distribution)}")
            return all_distribution
            
        except Exception as e:
            logger.error(f"分批处理失败: {str(e)}")
            return {}

    async def _comprehensive_ai_analysis(
        self,
        request: TableProcessingRequest,
        file_upload: StorageFile
    ) -> Dict[str, Any]:
        """
        2025年最新OCR技术的智能分析方法
        基于MiniCPM-o、InternVL3、Qwen2.5-VL等顶级模型的最佳实践
        """
        
        try:
            logger.info(f"🚀 启动2025年增强AI分析: {request.file_id}")
            
            # 第一步：快速复杂度评估（基于最新OCR模型的策略）
            complexity_result = await self._assess_document_complexity(request, file_upload)
            complexity_level = complexity_result.get('level', 'medium')
            
            logger.info(f"📊 文档复杂度评估: {complexity_level}")
            
            # 基于复杂度选择处理策略
            if complexity_level == 'simple':
                # 简单文档：一次性完整识别（类似MiniCPM-o的高效处理）
                return await self._single_pass_analysis(request, file_upload, complexity_result)
            
            elif complexity_level == 'medium':
                # 中等复杂度：智能分步处理（基于InternVL3的多阶段策略）
                return await self._intelligent_multi_stage_analysis(request, file_upload, complexity_result)
            
            else:  # complex
                # 复杂文档：降级到传统分批方法（确保稳定性）
                logger.warning("文档复杂度高，使用传统分批处理确保准确性")
                return await self._traditional_batch_processing(request, file_upload)
                
        except Exception as e:
            logger.error(f"2025年增强AI分析失败: {str(e)}")
            return {'success': False, 'error': str(e)}

    async def _assess_document_complexity(
        self,
        request: TableProcessingRequest,
        file_upload: StorageFile
    ) -> Dict[str, Any]:
        """
        智能复杂度评估（基于2025年最新OCR技术）
        参考Ocean-OCR和Qwen2.5-VL的复杂度评估策略
        """
        
        try:
            # 快速复杂度评估提示词（基于最新OCR研究）
            complexity_prompt = f"""
作为2025年最新的OCR专家，请快速评估这个文档的复杂度。

评估标准（基于最新OCR技术研究）：
1. 文本密度和布局复杂度
2. 表格结构复杂程度
3. 图片质量和分辨率
4. 多语言混合情况
5. 手写vs印刷文本比例

请返回JSON格式：
{{
    "level": "simple|medium|complex",
    "factors": {{
        "text_density": "low|medium|high",
        "layout_complexity": "simple|moderate|complex", 
        "image_quality": "poor|fair|good|excellent",
        "languages": ["chinese", "english", "mixed"],
        "text_type": "printed|handwritten|mixed",
        "table_structure": "simple|moderate|complex"
    }},
    "estimated_products": "number_estimate",
    "estimated_stores": "number_estimate", 
    "recommended_strategy": "single_pass|multi_stage|batch_processing",
    "confidence": "high|medium|low"
}}

用户需求: {request.requirements or '表格数据提取'}
"""

            result = await self.vision_service.analyze_image_with_prompt(
                image_path=file_upload.file_path,
                prompt=complexity_prompt,
                max_tokens=500
            )
            
            # 解析复杂度评估结果
            complexity_data = self._extract_json_from_response(result)
            
            if complexity_data:
                logger.info(f"📊 复杂度评估完成: {complexity_data.get('level')} - 策略: {complexity_data.get('recommended_strategy')}")
                return complexity_data
            else:
                # 默认中等复杂度
                return {
                    'level': 'medium',
                    'recommended_strategy': 'multi_stage',
                    'confidence': 'low'
                }
                
        except Exception as e:
            logger.error(f"复杂度评估失败: {str(e)}")
            return {'level': 'medium', 'recommended_strategy': 'multi_stage', 'confidence': 'low'}

    async def _single_pass_analysis(
        self,
        request: TableProcessingRequest,
        file_upload: StorageFile,
        complexity_info: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        一次性完整分析（基于MiniCPM-o的高效策略）
        适用于简单文档：商品<10个，门店<8个
        """
        
        try:
            logger.info("🎯 执行一次性完整分析（2025年高效策略）")
            
            # 基于最新OCR技术的完整提取提示词
            single_pass_prompt = f"""
作为2025年最新的OCR专家，请一次性完整分析这个采购分拨表格。

参考最新技术：MiniCPM-o、InternVL3、Qwen2.5-VL的优势
- 原生动态分辨率处理
- 结构化数据理解
- 高精度文本识别

复杂度信息：{complexity_info}

             请直接返回完整的JSON结果：
             {{
                 "products": [
                     {{
                         "product_name": "商品名称",
                         "specification": "数字规格（如：500、1、2.5）",
                         "unit": "文字单位（如：公斤、克、个、盒）", 
                         "unit_price": "单价(数字)",
                         "total_amount": "规格 × 数量 × 单价"
                     }}
                 ],
    "distribution": {{
        "门店名称": {{
            "商品名称": {{
                "quantity": "数量",
                "amount": "金额"
            }}
        }}
    }},
    "statistics": {{
        "total_products": "商品总数",
        "total_stores": "门店总数", 
        "total_amount": "总金额",
        "recognition_quality": "excellent|good|fair|poor"
    }}
}}

用户需求: {request.requirements or '完整表格数据提取'}
"""

            result = await self.vision_service.analyze_image_with_prompt(
                image_path=file_upload.file_path,
                prompt=single_pass_prompt,
                max_tokens=8000  # 增大token限制支持完整输出
            )
            
            # 解析完整结果
            complete_data = self._extract_json_from_response(result)
            
            if complete_data and complete_data.get('products'):
                logger.info(f"✅ 一次性分析成功: 商品{len(complete_data.get('products', []))}个")
                return {
                    'success': True,
                    'data': complete_data,
                    'method': 'single_pass_2025',
                    'metadata': {
                        'complexity': complexity_info,
                        'tokens_used': len(str(complete_data))
                    }
                }
            else:
                # 降级到多阶段处理
                logger.warning("一次性分析结果不完整，降级到多阶段处理")
                return await self._intelligent_multi_stage_analysis(request, file_upload, complexity_info)
                
        except Exception as e:
            logger.error(f"一次性分析失败: {str(e)}")
            return {'success': False, 'error': str(e)}

    async def _intelligent_multi_stage_analysis(
        self,
        request: TableProcessingRequest,
        file_upload: StorageFile,
        complexity_info: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        智能多阶段分析（基于InternVL3和Qwen2.5-VL的策略）
        适用于中等复杂度：商品10-30个，门店8-20个
        """
        
        try:
            logger.info("🧠 执行智能多阶段分析（2025年优化策略）")
            
            # 阶段1：结构理解（基于最新模型的布局理解）
            structure_result = await self._analyze_document_structure_2025(request, file_upload, complexity_info)
            
            # 阶段2：核心数据提取（优化的批处理策略）
            if structure_result.get('success'):
                data_result = await self._extract_core_data_2025(
                    request, file_upload, structure_result['structure'], complexity_info
                )
                
                if data_result.get('success'):
                    return {
                        'success': True,
                        'data': data_result['data'],
                        'method': 'multi_stage_2025',
                        'metadata': {
                            'complexity': complexity_info,
                            'structure': structure_result['structure'],
                            'stages_completed': 2
                        }
                    }
            
            # 降级处理
            logger.warning("智能多阶段分析失败，降级到传统方法")
            return await self._traditional_batch_processing(request, file_upload)
            
        except Exception as e:
            logger.error(f"智能多阶段分析失败: {str(e)}")
            return {'success': False, 'error': str(e)}

    async def _analyze_document_structure_2025(
        self,
        request: TableProcessingRequest,
        file_upload: StorageFile,
        complexity_info: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        2025年增强文档结构分析
        基于Ocean-OCR和最新视觉模型的结构理解能力
        """
        
        try:
            # 结构分析提示词（基于2025年最新研究）
            structure_prompt = f"""
作为2025年最新的文档结构分析专家，请分析这个表格的详细结构。

应用最新技术：
- 原生分辨率处理（避免传统normalization）
- Window Attention机制
- 多层特征提取

复杂度参考：{complexity_info}

请返回详细的结构分析：
{{
    "table_type": "purchase_distribution|purchase_order|mixed",
    "layout": {{
        "header_rows": "表头行数",
        "data_rows": "数据行数", 
        "product_columns": ["商品相关列名"],
        "store_columns": ["门店列名"],
        "total_columns": "总列数"
    }},
    "content_analysis": {{
        "estimated_products": "商品数量估计",
        "estimated_stores": "门店数量估计",
        "data_density": "low|medium|high",
        "text_clarity": "excellent|good|fair|poor"
    }},
    "processing_strategy": {{
        "recommended_batch_size": "建议批处理大小",
        "priority_extraction": "products_first|stores_first|parallel",
        "special_handling": ["handwritten", "low_quality", "mixed_language"]
    }}
}}

用户需求: {request.requirements or '结构分析'}
"""

            result = await self.vision_service.analyze_image_with_prompt(
                image_path=file_upload.file_path,
                prompt=structure_prompt,
                max_tokens=1000
            )
            
            structure_data = self._extract_json_from_response(result)
            
            if structure_data:
                logger.info(f"📋 结构分析完成: {structure_data.get('table_type')} - 商品约{structure_data.get('content_analysis', {}).get('estimated_products')}个")
                return {'success': True, 'structure': structure_data}
            else:
                return {'success': False, 'error': 'Structure analysis failed'}
                
        except Exception as e:
            logger.error(f"结构分析失败: {str(e)}")
            return {'success': False, 'error': str(e)}

    async def _extract_core_data_2025(
        self,
        request: TableProcessingRequest,
        file_upload: StorageFile,
        structure_info: Dict[str, Any],
        complexity_info: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        2025年优化的核心数据提取
        基于结构分析结果的智能数据提取
        """
        
        try:
            # 根据结构信息优化提取策略
            batch_size = structure_info.get('processing_strategy', {}).get('recommended_batch_size', 6)
            estimated_stores = structure_info.get('content_analysis', {}).get('estimated_stores', 10)
            
            logger.info(f"📦 开始核心数据提取 - 预计{estimated_stores}个门店，批大小{batch_size}")
            
            # 使用传统但优化的分批处理
            return await self._extract_batched_distribution_data(
                request, file_upload, [], [], int(batch_size)
            )
            
        except Exception as e:
            logger.error(f"核心数据提取失败: {str(e)}")
            return {'success': False, 'error': str(e)}