#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
通用表格处理服务
AI为主（视觉）+ 规则兜底的混合处理方案
重构版本：使用策略模式提高代码可维护性
"""

import logging
import uuid
import base64
import time
import json
import re
import pandas as pd
import numpy as np
from datetime import datetime
from typing import Optional, List, Dict, Any, Tuple, Protocol
from sqlalchemy.ext.asyncio import AsyncSession
from PIL import Image
import io
from abc import ABC, abstractmethod

# 可选依赖
try:
    import matplotlib
    matplotlib.use('Agg')  # 使用非交互式后端
    import matplotlib.pyplot as plt
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False

from services.ai.system_integration_service import SystemAIIntegrationService
from services.ai.vision_service import AIVisionService
from schemas.table_processing import (
    TableProcessingRequest,
    TableProcessingResult,
    TableTemplate,
    TableColumnDefinition,
    TemplateMatchResult,
    AIExtractionResult,
    TableValidationError,
    TableCorrectionSuggestion,
    ProcessingStats
)

logger = logging.getLogger(__name__)

class ProcessingStrategy(ABC):
    """表格处理策略基类"""
    
    @property
    @abstractmethod
    def name(self) -> str:
        """策略名称"""
        pass
    
    @abstractmethod
    async def process(
        self,
        db: AsyncSession,
        image_data: str,
        request: TableProcessingRequest,
        project_id: uuid.UUID,
        tenant_id: Optional[uuid.UUID],
        user_id: uuid.UUID
    ) -> TableProcessingResult:
        """执行处理策略"""
        pass
    
    @abstractmethod
    def can_handle(self, request: TableProcessingRequest, template_match: TemplateMatchResult) -> bool:
        """判断是否可以处理此请求"""
        pass

class TemplateOnlyStrategy(ProcessingStrategy):
    """仅模板匹配策略"""
    
    @property
    def name(self) -> str:
        return "template_only"
    
    def can_handle(self, request: TableProcessingRequest, template_match: TemplateMatchResult) -> bool:
        return request.processing_mode == "template_only"
    
    async def process(
        self,
        db: AsyncSession,
        image_data: str,
        request: TableProcessingRequest,
        project_id: uuid.UUID,
        tenant_id: Optional[uuid.UUID],
        user_id: uuid.UUID
    ) -> TableProcessingResult:
        """使用模板匹配策略处理"""
        template_match = await TableProcessingService._match_template(image_data, request.template_hint)
        
        if not template_match.matched:
            return TableProcessingResult(
                success=False,
                processing_method="template",
                extracted_data=[],
                column_mapping={},
                errors=[{"type": "template_error", "message": "未找到匹配的模板"}]
            )
        
        return await TableProcessingService._extract_with_template(
            image_data, template_match.template_id, request
        )

class AIOnlyStrategy(ProcessingStrategy):
    """仅AI识别策略"""
    
    @property
    def name(self) -> str:
        return "ai_only"
    
    def can_handle(self, request: TableProcessingRequest, template_match: TemplateMatchResult) -> bool:
        return request.processing_mode == "ai_only"
    
    async def process(
        self,
        db: AsyncSession,
        image_data: str,
        request: TableProcessingRequest,
        project_id: uuid.UUID,
        tenant_id: Optional[uuid.UUID],
        user_id: uuid.UUID
    ) -> TableProcessingResult:
        """使用AI识别策略处理"""
        ai_start = time.time()
        result = await TableProcessingService._extract_with_ai(
            db, image_data, request, project_id, tenant_id, user_id
        )
        ai_time = time.time() - ai_start
        
        # 更新现有的 ai_processing_info，而不是覆盖它
        if result.ai_processing_info is None:
            result.ai_processing_info = {}
        result.ai_processing_info["processing_time"] = ai_time
        
        # 不要覆盖 _extract_with_ai 中设置的更具体的 processing_method
        # result.processing_method = "ai" # <- 移除这一行或注释掉

        return result

class AutoStrategy(ProcessingStrategy):
    """自动选择策略（混合模式）"""
    
    @property
    def name(self) -> str:
        return "auto"
    
    def can_handle(self, request: TableProcessingRequest, template_match: TemplateMatchResult) -> bool:
        return request.processing_mode == "auto"
    
    async def process(
        self,
        db: AsyncSession,
        image_data: str,
        request: TableProcessingRequest,
        project_id: uuid.UUID,
        tenant_id: Optional[uuid.UUID],
        user_id: uuid.UUID
    ) -> TableProcessingResult:
        """使用自动选择策略处理"""
        template_match = await TableProcessingService._match_template(image_data, request.template_hint)
        
        if template_match.matched and template_match.confidence >= 0.8:
            # 高置信度模板匹配，尝试模板提取
            return await self._process_with_template_fallback(
                db, image_data, request, template_match, project_id, tenant_id, user_id
            )
        else:
            # 低置信度或无匹配，直接使用AI
            return await self._process_with_ai_only(
                db, image_data, request, project_id, tenant_id, user_id
            )
    
    async def _process_with_template_fallback(
        self,
        db: AsyncSession,
        image_data: str,
        request: TableProcessingRequest,
        template_match: TemplateMatchResult,
        project_id: uuid.UUID,
        tenant_id: Optional[uuid.UUID],
        user_id: uuid.UUID
    ) -> TableProcessingResult:
        """模板优先，AI兜底"""
        template_result = await TableProcessingService._extract_with_template(
            image_data, template_match.template_id, request
        )
        
        if template_result.processing_method == "template_needs_ai":
            # 模板指导的AI处理
            return await self._process_template_guided_ai(
                db, image_data, request, template_result, template_match, project_id, tenant_id, user_id
            )
        elif (template_result.valid_rows / max(template_result.total_rows, 1)) >= 0.8:
            # 模板提取成功率高
            template_result.processing_method = "template"
            return template_result
        else:
            # 模板提取成功率低，使用AI辅助
            return await self._process_hybrid_mode(
                db, image_data, request, template_result, project_id, tenant_id, user_id
            )
    
    async def _process_template_guided_ai(
        self,
        db: AsyncSession,
        image_data: str,
        request: TableProcessingRequest,
        template_result: TableProcessingResult,
        template_match: TemplateMatchResult,
        project_id: uuid.UUID,
        tenant_id: Optional[uuid.UUID],
        user_id: uuid.UUID
    ) -> TableProcessingResult:
        """模板指导的AI处理"""
        ai_start = time.time()
        
        # 使用模板提供的专用提示词
        template_prompt = template_result.ai_processing_info.get("template_prompt")
        if template_prompt:
            request.custom_prompt = template_prompt
        
        result = await TableProcessingService._extract_with_ai(
            db, image_data, request, project_id, tenant_id, user_id
        )
        
        ai_time = time.time() - ai_start
        result.ai_processing_info = {
            "processing_time": ai_time, 
            "used_template_prompt": True
        }
        result.template_matched = template_match.template_id
        result.processing_method = "template_guided_ai"
        return result
    
    async def _process_hybrid_mode(
        self,
        db: AsyncSession,
        image_data: str,
        request: TableProcessingRequest,
        template_result: TableProcessingResult,
        project_id: uuid.UUID,
        tenant_id: Optional[uuid.UUID],
        user_id: uuid.UUID
    ) -> TableProcessingResult:
        """混合模式：模板+AI"""
        ai_start = time.time()
        ai_result = await TableProcessingService._extract_with_ai(
            db, image_data, request, project_id, tenant_id, user_id
        )
        ai_time = time.time() - ai_start
        
        # 合并结果
        result = TableProcessingService._merge_results(template_result, ai_result)
        result.ai_processing_info = {"processing_time": ai_time}
        result.processing_method = "hybrid"
        return result
    
    async def _process_with_ai_only(
        self,
        db: AsyncSession,
        image_data: str,
        request: TableProcessingRequest,
        project_id: uuid.UUID,
        tenant_id: Optional[uuid.UUID],
        user_id: uuid.UUID
    ) -> TableProcessingResult:
        """纯AI处理"""
        ai_start = time.time()
        result = await TableProcessingService._extract_with_ai(
            db, image_data, request, project_id, tenant_id, user_id
        )
        ai_time = time.time() - ai_start
        
        # 更新现有的 ai_processing_info，而不是覆盖它
        if result.ai_processing_info is None:
            result.ai_processing_info = {}
        result.ai_processing_info["processing_time"] = ai_time
        
        # 不要覆盖 _extract_with_ai 中设置的更具体的 processing_method
        # result.processing_method = "ai" # <- 移除这一行或注释掉
        
        return result

class Enhanced2025AIStrategy(ProcessingStrategy):
    """2025年增强AI策略 - 基于最新视觉AI技术"""

    def __init__(self):
        from services.enhanced_table_processing_service import Enhanced2025TableProcessingService
        self.enhanced_service = Enhanced2025TableProcessingService()

    @property
    def name(self) -> str:
        return "enhanced_2025_ai"

    def can_handle(self, request: TableProcessingRequest, template_match: TemplateMatchResult) -> bool:
        return request.processing_mode == "enhanced_ai" or request.processing_mode == "enhanced_2025_ai" or (
            request.processing_mode == "auto" and
            hasattr(request, 'ai_enhanced') and
            request.ai_enhanced
        )
    
    async def process(
        self,
        db: AsyncSession,
        image_data: str,
        request: TableProcessingRequest,
        project_id: uuid.UUID,
        tenant_id: Optional[uuid.UUID],
        user_id: uuid.UUID
    ) -> TableProcessingResult:
        """使用2025年增强AI技术处理表格"""
        try:
            logger.info("开始2025年增强AI表格处理")

            # 获取文件信息
            from models.storage import StorageFile
            file_upload = await db.get(StorageFile, request.file_id)
            if not file_upload:
                raise ValueError(f"文件未找到: {request.file_id}")

            # 使用2025年增强处理服务
            result = await self.enhanced_service.process_table_with_enhanced_ai(request, file_upload)

            if result.get('success'):
                data = result.get('data', {})
                metadata = result.get('metadata', {})

                # 转换为TableProcessingResult格式
                extracted_data = []

                # 从products和distribution构建表格行数据
                products = data.get('products', [])
                distribution = data.get('distribution', {})

                # 构建表格行（每个商品一行，包含所有门店的分拨数据）
                for product in products:
                    row = {
                        '商品名称': product.get('name', ''),
                        '规格': product.get('specification', '1'),
                        '单位': product.get('unit', ''),
                        '单价': product.get('unit_price', 0)
                    }

                    # 添加每个门店的分拨数量
                    for store_name, store_items in distribution.items():
                        quantity = 0
                        for item in store_items:
                            if item.get('product_name') == product.get('name'):
                                quantity = item.get('quantity', 0)
                                break
                        row[store_name] = quantity

                    extracted_data.append(row)

                # 构建列映射
                column_mapping = {'商品名称': 'product_name', '规格': 'specification', '单位': 'unit', '单价': 'unit_price'}
                for store_name in distribution.keys():
                    column_mapping[store_name] = f'store_{store_name}'

                return TableProcessingResult(
                    success=True,
                    processing_method="enhanced_2025_ai",
                    extracted_data=extracted_data,
                    column_mapping=column_mapping,
                    total_rows=len(extracted_data),
                    valid_rows=len(extracted_data),
                    ai_processing_info={
                        'ai_version': '2025.1',
                        'table_structure': metadata.get('table_structure', {}),
                        'quality_assessment': metadata.get('quality_assessment', {}),
                        'processing_stages': metadata.get('processing_stages', [])
                    }
                )
            else:
                error_msg = result.get('error', '2025年增强AI处理失败')
                return TableProcessingResult(
                    success=False,
                    processing_method="enhanced_2025_ai",
                    extracted_data=[],
                    column_mapping={},
                    errors=[{"type": "ai_processing_error", "message": error_msg}],
                    ai_processing_info={
                        'ai_version': '2025.1',
                        'error_details': result.get('metadata', {})
                    }
                )
            
        except Exception as e:
            logger.error(f"2025年增强AI处理失败: {str(e)}")
            return TableProcessingResult(
                success=False,
                processing_method="enhanced_2025_ai",
                extracted_data=[],
                column_mapping={},
                errors=[{"type": "ai_error", "message": str(e)}]
            )
    
    async def _establish_vision_context(
        self, 
        session_id: str, 
        image_data: str
    ) -> Dict[str, Any]:
        """建立视觉上下文 - 单次图片传输"""
        logger.info(f"建立视觉上下文: {session_id}")
        
        # 构建初始视觉理解提示
        initial_prompt = """
        我将向你展示一张表格图片。请仔细观察并建立视觉记忆。

        这是一个多轮对话的开始，我会在后续对话中询问表格的具体内容。
        请先告诉我你看到了什么类型的表格，包含哪些主要区域。

        注意：
        1. 这是单次图片传输，后续对话将基于你的视觉记忆
        2. 请建立对表格结构的深度理解
        3. 识别所有文字、数字和表格布局
        """
        
        # 调用AI建立视觉上下文
        ai_service = SystemAIIntegrationService()
        vision_response = await ai_service.analyze_image_with_context(
            image_data,
            initial_prompt,
            session_id=session_id
        )
        
        # 保存视觉上下文
        self.vision_sessions[session_id] = {
            'image_data': image_data,
            'initial_understanding': vision_response,
            'conversation_history': [
                {'role': 'user', 'content': initial_prompt},
                {'role': 'assistant', 'content': vision_response}
            ],
            'created_at': datetime.now()
        }
        
        return {
            'session_id': session_id,
            'initial_understanding': vision_response
        }
    
    async def _multi_turn_processing(
        self, 
        session_id: str, 
        vision_context: Dict[str, Any],
        request: TableProcessingRequest
    ) -> Dict[str, Any]:
        """基于视觉上下文的多轮对话处理"""
        logger.info(f"开始多轮对话处理: {session_id}")
        
        # 第一轮：表格结构分析
        structure_data = await self._analyze_table_structure(session_id)
        
        # 第二轮：数据提取
        extracted_data = await self._extract_table_data(
            session_id, structure_data, request
        )
        
        # 第三轮：数据验证和补充
        validated_data = await self._validate_and_supplement(
            session_id, extracted_data
        )
        
        return validated_data
    
    async def _analyze_table_structure(self, session_id: str) -> Dict[str, Any]:
        """第一轮对话：分析表格结构"""
        prompt = """
        基于你刚才看到的表格，请分析表格结构：

        1. 表格有多少行多少列？
        2. 表头在哪里？包含哪些列？
        3. 哪些列是商品信息（如商品名称、规格、单位、单价）？
        4. 哪些列是数量或金额信息？
        5. 是否有合计行或汇总信息？

        请以JSON格式返回结构化信息：
        {
            "table_dimensions": {"rows": 数字, "columns": 数字},
            "headers": ["列名1", "列名2", ...],
            "data_columns": {
                "product_info": ["商品相关列名"],
                "quantity_amount": ["数量金额相关列名"],
                "other": ["其他列名"]
            },
            "summary_rows": ["合计行位置描述"]
        }
        """
        
        response = await self._continue_conversation(session_id, prompt)
        
        try:
            structure_data = self._extract_json_from_response(response)
            logger.info(f"表格结构分析完成: {structure_data}")
            return structure_data
        except Exception as e:
            logger.error(f"表格结构分析失败: {e}")
            return {}
    
    async def _extract_table_data(
        self, 
        session_id: str, 
        structure_data: Dict[str, Any],
        request: TableProcessingRequest
    ) -> List[Dict[str, Any]]:
        """第二轮对话：提取表格数据"""
        headers = structure_data.get('headers', [])
        
        prompt = f"""
        现在请提取表格中的所有数据行。

        根据之前分析，表格列包括：{headers}

        请逐行提取数据，返回JSON格式的数组：
        [
            {{
                "列名1": "值1",
                "列名2": "值2",
                ...
            }},
            ...
        ]

        注意：
        1. 跳过表头行
        2. 跳过合计行和汇总行
        3. 保持原始数据格式
        4. 空值用null表示
        """
        
        response = await self._continue_conversation(session_id, prompt)
        
        try:
            extracted_data = self._extract_json_from_response(response)
            logger.info(f"数据提取完成: {len(extracted_data)}行")
            return extracted_data
        except Exception as e:
            logger.error(f"数据提取失败: {e}")
            return []
    
    async def _validate_and_supplement(
        self, 
        session_id: str,
        extracted_data: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """第三轮对话：数据验证和补充"""
        prompt = f"""
        请验证和补充提取的数据：

        已提取数据行数：{len(extracted_data)}

        请检查：
        1. 数据是否完整？有无遗漏？
        2. 数值格式是否正确？
        3. 是否需要数据清理或标准化？

        如有问题，请提供修正建议。
        最后返回验证结果的JSON：
        {{
            "validation_passed": true/false,
            "data_quality": "high/medium/low",
            "issues": ["问题描述"],
            "suggestions": ["改进建议"]
        }}
        """
        
        response = await self._continue_conversation(session_id, prompt)
        
        try:
            validation_result = self._extract_json_from_response(response)
            
            # 确保validation_result是字典格式
            if not isinstance(validation_result, dict):
                validation_result = {'validation_passed': False, 'data_quality': 'low', 'error': 'Invalid validation format'}
            
            return {
                'extracted_data': extracted_data,
                'validation': validation_result,
                'statistics': {
                    'row_count': len(extracted_data),
                    'data_quality': validation_result.get('data_quality', 'medium')
                }
            }
        except Exception as e:
            logger.error(f"数据验证失败: {e}")
            return {
                'extracted_data': extracted_data,
                'validation': {'validation_passed': False, 'error': str(e)},
                'statistics': {
                    'row_count': len(extracted_data),
                    'data_quality': 'low'
                }
            }
    
    async def _continue_conversation(self, session_id: str, prompt: str) -> str:
        """继续多轮对话（不重新传输图片）"""
        session = self.vision_sessions.get(session_id)
        if not session:
            raise ValueError(f"会话不存在: {session_id}")
        
        # 添加到对话历史
        session['conversation_history'].append({
            'role': 'user', 
            'content': prompt
        })
        
        # 调用AI继续对话（基于已有的视觉上下文）
        ai_service = SystemAIIntegrationService()
        response = await ai_service.continue_vision_conversation(
            session['conversation_history'],
            session_id=session_id
        )
        
        # 更新对话历史
        session['conversation_history'].append({
            'role': 'assistant', 
            'content': response
        })
        
        return response
    
    def _extract_json_from_response(self, response: str) -> Any:
        """从AI响应中提取JSON数据"""
        try:
            # 尝试直接解析
            return json.loads(response)
        except:
            # 尝试提取JSON代码块
            json_match = re.search(r'```json\s*(.*?)\s*```', response, re.DOTALL)
            if json_match:
                return json.loads(json_match.group(1))
            
            # 尝试提取{}或[]包围的内容
            json_match = re.search(r'(\{.*\}|\[.*\])', response, re.DOTALL)
            if json_match:
                return json.loads(json_match.group(1))
            
            raise ValueError(f"无法从响应中提取JSON: {response[:200]}...")
    
    async def _convert_to_standard_format(
        self, 
        processing_result: Dict[str, Any],
        request: TableProcessingRequest
    ) -> TableProcessingResult:
        """转换为标准TableProcessingResult格式"""
        extracted_data = processing_result.get('extracted_data', [])
        validation = processing_result.get('validation', {})
        
        # 生成列映射
        column_mapping = {}
        if extracted_data:
            sample_row = extracted_data[0]
            for i, column_name in enumerate(sample_row.keys()):
                column_mapping[f"column_{i}"] = column_name
        
        # 计算处理统计
        from datetime import datetime
        now = datetime.now()
        stats = ProcessingStats(
            start_time=now.isoformat(),
            end_time=now.isoformat(),
            processing_duration=0.0
        )
        
        return TableProcessingResult(
            success=validation.get('validation_passed', True),
            processing_method="enhanced_2025_ai",
            extracted_data=extracted_data,
            column_mapping=column_mapping,
            template_matched=None,
            confidence_score=0.9 if validation.get('validation_passed', True) else 0.7,
            ai_processing_info={
                'model_version': '2025_enhanced',
                'processing_method': 'multi_turn_conversation',
                'data_quality': validation.get('data_quality', 'medium'),
                'validation_passed': validation.get('validation_passed', True)
            },
            errors=[] if validation.get('validation_passed', True) else [
                {"type": "validation_error", "message": "数据验证未通过"}
            ]
        )
    
    def _cleanup_session(self, session_id: str):
        """清理会话数据"""
        if session_id in self.vision_sessions:
            del self.vision_sessions[session_id]
            logger.info(f"会话已清理: {session_id}")

class ProcessingStrategyFactory:
    """处理策略工厂"""
    
    _strategies = [
        TemplateOnlyStrategy(),
        AIOnlyStrategy(),
        AutoStrategy(),
        Enhanced2025AIStrategy()
    ]
    
    @classmethod
    def get_strategy(
        cls, 
        request: TableProcessingRequest, 
        template_match: TemplateMatchResult
    ) -> ProcessingStrategy:
        """根据请求获取合适的处理策略"""
        for strategy in cls._strategies:
            if strategy.can_handle(request, template_match):
                return strategy
        
        # 默认使用自动策略
        return AutoStrategy()

class TableProcessingService:
    """通用表格处理服务 - 重构版本"""
    
    # 预定义模板库
    PREDEFINED_TEMPLATES = {
        "purchase_distribution": TableTemplate(
            template_id="purchase_distribution",
            template_name="采购分拨单",
            description="采购分拨单模板，支持多个分拨目标的横向分布",
            columns=[
                TableColumnDefinition(name="商品名称", field_key="product_name", required=True),
                TableColumnDefinition(name="单位", field_key="unit", data_type="string"),
                TableColumnDefinition(name="规格", field_key="specification"),
                TableColumnDefinition(name="单价", field_key="unit_price", data_type="number"),
                TableColumnDefinition(name="合计", field_key="total", data_type="number"),
                TableColumnDefinition(name="分拨目标", field_key="distribution_targets", 
                                   data_type="dynamic", required=False,
                                   description="动态识别的分拨目标列，如门店名称"),
            ],
            identification_rules=[
                "包含'商品名称'和'单价'列",
                "有门店或分拨目标名称列",
                "表格结构为商品信息+多个分拨目标数量列",
                "分拨目标通常在表格右侧，数量为数字"
            ]
        ),
        "inventory_sheet": TableTemplate(
            template_id="inventory_sheet", 
            template_name="库存表",
            description="库存管理表格",
            columns=[
                TableColumnDefinition(name="商品编号", field_key="product_code", required=True),
                TableColumnDefinition(name="商品名称", field_key="product_name", required=True),
                TableColumnDefinition(name="库存数量", field_key="quantity", data_type="number", required=True),
                TableColumnDefinition(name="单位", field_key="unit"),
                TableColumnDefinition(name="规格", field_key="specification"),
            ],
            identification_rules=[
                "包含'商品编号'或'商品名称'列",
                "包含'库存'或'数量'相关列",
                "表格主要是商品库存信息"
            ]
        ),
        "monthly_inventory": TableTemplate(
            template_id="monthly_inventory",
            template_name="月度盘点表",
            description="门店月度盘点表格",
            columns=[
                TableColumnDefinition(name="商品编号", field_key="product_code", required=True),
                TableColumnDefinition(name="商品名称", field_key="product_name", required=True),
                TableColumnDefinition(name="库存量", field_key="inventory_quantity", data_type="number", required=True),
                TableColumnDefinition(name="实际量", field_key="actual_quantity", data_type="number", required=True),
                TableColumnDefinition(name="单位", field_key="unit"),
                TableColumnDefinition(name="规格", field_key="specification"),
                TableColumnDefinition(name="备注", field_key="remark"),
            ],
            identification_rules=[
                "包含'商品编号'或'商品名称'列",
                "包含'库存量'和'实际量'列",
                "表格主要是盘点对比数据"
            ]
        )
    }

    @staticmethod
    async def process_table(
        db: AsyncSession,
        request: TableProcessingRequest,
        project_id: uuid.UUID,
        tenant_id: Optional[uuid.UUID],
        user_id: uuid.UUID
    ) -> TableProcessingResult:
        """
        处理表格文件的主入口 - 重构版本
        使用策略模式简化代码结构
        """
        start_time = time.time()
        
        try:
            # 验证输入参数
            if not request.file_id and not request.image_base64:
                raise ValueError("必须提供file_id或image_base64其中之一")

            # 1. 获取图像数据
            if request.image_base64:
                # 直接使用提供的图像数据
                image_data = request.image_base64
                logger.info("使用直接提供的图像数据")
            else:
                # 从文件加载图像数据
                logger.info(f"开始处理表格文件: {request.file_id}")
                image_data = await TableProcessingService._load_file_as_image(
                    db, request.file_id, project_id
                )
            
            # 2. 模板智能分流
            template_match_start = time.time()
            template_match = await TableProcessingService._match_template(
                image_data, request.template_hint
            )
            template_match_time = time.time() - template_match_start
            
            # 3. 选择处理策略
            strategy = ProcessingStrategyFactory.get_strategy(request, template_match)
            logger.info(f"选择处理策略: {strategy.name}")

            # 4. 执行策略
            # Initial result from the strategy
            initial_result = await strategy.process(
                db, image_data, request, project_id, tenant_id, user_id
            )

            # [核心更改] 立即保存原始的AI元数据
            definitive_processing_method = initial_result.processing_method
            definitive_ai_processing_info = initial_result.ai_processing_info
            definitive_template_matched = initial_result.template_matched # 也保存 template_matched

            logger.info(f"[DEBUG] Initial strategy result - Method: {definitive_processing_method}")
            logger.info(f"[DEBUG] Initial strategy result - AI Info: {json.dumps(definitive_ai_processing_info, ensure_ascii=False, indent=2) if definitive_ai_processing_info else 'None'}")
            logger.info(f"[DEBUG] Initial strategy result - Template Matched: {definitive_template_matched}")


            # current_result 用于数据处理流程
            current_data_result = initial_result

            if request.enable_validation or request.enable_correction:
                logger.info("进行数据校验和修正...")
                current_data_result = await TableProcessingService._validate_and_correct_data(current_data_result, request)
                
                logger.info(f"[DEBUG] Result after validation/correction - Method: {current_data_result.processing_method}") # 这个可能是 _validate_and_correct_data 内部的
                logger.info(f"[DEBUG] Result after validation/correction - AI Info: {json.dumps(current_data_result.ai_processing_info, ensure_ascii=False, indent=2) if current_data_result.ai_processing_info else 'None'}")
            else:
                logger.info("跳过数据校验和修正")

            # [核心更改] 构建最终返回结果：
            # 使用 current_data_result 的数据字段 (extracted_data, row counts, errors, etc.)
            # 使用 definitive_... 的元数据字段 (processing_method, ai_processing_info, template_matched)
            
            final_result = TableProcessingResult(
                success=current_data_result.success,
                extracted_data=current_data_result.extracted_data,
                column_mapping=current_data_result.column_mapping,
                total_rows=current_data_result.total_rows,
                valid_rows=current_data_result.valid_rows,
                error_rows=current_data_result.error_rows,
                errors=current_data_result.errors,
                warnings=current_data_result.warnings,
                
                # 使用最初捕获的、权威的元数据
                processing_method=definitive_processing_method,
                ai_processing_info=definitive_ai_processing_info,
                template_matched=definitive_template_matched
            )
            
            logger.info(f"[DEBUG] Final result before return - Method: {final_result.processing_method}")
            logger.info(f"[DEBUG] Final result before return - AI Info: {json.dumps(final_result.ai_processing_info, ensure_ascii=False, indent=2) if final_result.ai_processing_info else 'None'}")
            
            return final_result

        except Exception as e:
            logger.error(f"表格处理失败: {e}")
            return TableProcessingResult(
                success=False,
                processing_method="error",
                extracted_data=[],
                column_mapping={},
                errors=[{
                    "type": "processing_error",
                    "message": str(e),
                    "timestamp": datetime.now().isoformat()
                }]
            )

    @staticmethod
    async def _load_file_as_image(
        db: AsyncSession,
        file_id: uuid.UUID,
        project_id: uuid.UUID
    ) -> str:
        """
        加载文件并转换为base64编码的图像
        支持Excel、CSV、图像文件
        优化版本：支持更大数据量和分页处理
        """
        try:
            # 从存储服务获取文件
            from services.storage_service import StorageService
            file_info = await StorageService.get_file_by_id(db, file_id, project_id)
            
            if not file_info:
                raise ValueError(f"文件不存在: {file_id}")
            
            # 获取文件下载信息
            download_info = await StorageService.download_file(db, file_id, project_id)
            if not download_info:
                raise ValueError(f"无法获取文件下载信息: {file_id}")
            
            # 读取文件内容
            file_path = download_info['file_path']
            with open(file_path, 'rb') as f:
                file_content = f.read()
            
            # 根据文件类型处理
            file_name = download_info.get('file_name', '').lower()
            file_type = download_info.get('mime_type', '').lower()
            
            # 如果是图像文件，直接返回base64编码
            if (file_type.startswith('image/') or 
                any(file_name.endswith(ext) for ext in ['.jpg', '.jpeg', '.png', '.bmp', '.gif'])):
                
                # 确保是支持的图像格式
                try:
                    image = Image.open(io.BytesIO(file_content))
                    # 转换为RGB模式以确保兼容性
                    if image.mode not in ['RGB', 'RGBA']:
                        image = image.convert('RGB')
                    
                    # 智能压缩图像：根据内容复杂度调整压缩策略
                    original_size = image.size
                    max_size = (2048, 2048)  # 提高最大尺寸限制
                    
                    # 如果图像很大，使用更智能的压缩策略
                    if original_size[0] > max_size[0] or original_size[1] > max_size[1]:
                        # 保持宽高比的智能缩放
                        ratio = min(max_size[0]/original_size[0], max_size[1]/original_size[1])
                        new_size = (int(original_size[0] * ratio), int(original_size[1] * ratio))
                        image = image.resize(new_size, Image.Resampling.LANCZOS)
                        logger.info(f"图像从 {original_size} 压缩到 {new_size}")
                    
                    # 转换为base64
                    buffer = io.BytesIO()
                    # 使用JPEG格式以减少文件大小，但保持较高质量
                    if image.mode == 'RGBA':
                        # RGBA图像需要转换为RGB才能保存为JPEG
                        rgb_image = Image.new('RGB', image.size, (255, 255, 255))
                        rgb_image.paste(image, mask=image.split()[-1])
                        rgb_image.save(buffer, format='JPEG', quality=85, optimize=True)
                    else:
                        image.save(buffer, format='JPEG', quality=85, optimize=True)
                    
                    image_data = base64.b64encode(buffer.getvalue()).decode('utf-8')
                    
                    logger.info(f"成功加载图像文件: {file_name}, 压缩后大小: {len(image_data)} bytes")
                    return image_data
                    
                except Exception as e:
                    logger.error(f"处理图像文件失败: {e}")
                    raise ValueError(f"图像文件格式不支持或已损坏: {str(e)}")
            
            # 如果是Excel或CSV文件，先转换为图像
            elif (file_type in ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                               'application/vnd.ms-excel', 'text/csv'] or
                  any(file_name.endswith(ext) for ext in ['.xlsx', '.xls', '.csv'])):
                
                try:
                    # 读取Excel/CSV为DataFrame
                    if file_name.endswith('.csv'):
                        df = pd.read_csv(io.BytesIO(file_content), encoding='utf-8')
                    else:
                        df = pd.read_excel(io.BytesIO(file_content))
                    
                    # 智能数据量处理策略
                    original_rows = len(df)
                    original_cols = len(df.columns)
                    
                    # 动态调整处理限制
                    if original_rows > 500:
                        # 对于大数据量，采用分页处理策略
                        logger.warning(f"表格数据量较大({original_rows}行)，将采用分页处理")
                        
                        # 取前200行作为样本进行AI识别
                        df_sample = df.head(200)
                        
                        # 同时保存完整数据的统计信息
                        data_stats = {
                            "total_rows": original_rows,
                            "total_columns": original_cols,
                            "sample_rows": len(df_sample),
                            "processing_mode": "sampled",
                            "data_preview": df.head(10).to_dict('records')  # 前10行预览
                        }
                        
                        df = df_sample
                    else:
                        # 小数据量，完整处理
                        if original_rows > 200:
                            df = df.head(200)
                            logger.warning(f"表格行数超过200行，只处理前200行")
                        
                        data_stats = {
                            "total_rows": original_rows,
                            "total_columns": original_cols,
                            "sample_rows": len(df),
                            "processing_mode": "full"
                        }
                    
                    if original_cols > 30:
                        df = df.iloc[:, :30]
                        logger.warning(f"表格列数超过30列，只处理前30列")
                    
                    # 数据清理：处理空值和异常值
                    df = df.fillna('')  # 填充空值
                    
                    # 将DataFrame转换为HTML表格（优化版本）
                    html_table = df.to_html(
                        index=False, 
                        escape=False, 
                        table_id="data-table",
                        classes="table table-striped table-bordered",
                        border=1
                    )
                    
                    # 添加CSS样式以改善表格显示
                    html_content = f"""
                    <!DOCTYPE html>
                    <html>
                    <head>
                        <meta charset="utf-8">
                        <style>
                            body {{ font-family: Arial, sans-serif; margin: 20px; }}
                            .table {{ border-collapse: collapse; width: 100%; }}
                            .table th, .table td {{ 
                                border: 1px solid #ddd; 
                                padding: 8px; 
                                text-align: left;
                                font-size: 12px;
                            }}
                            .table th {{ 
                                background-color: #f2f2f2; 
                                font-weight: bold;
                            }}
                            .table tr:nth-child(even) {{ background-color: #f9f9f9; }}
                            .stats {{ 
                                margin-bottom: 20px; 
                                padding: 10px; 
                                background-color: #e7f3ff; 
                                border-radius: 5px;
                            }}
                        </style>
                    </head>
                    <body>
                        <div class="stats">
                            <h3>数据统计</h3>
                            <p>总行数: {data_stats['total_rows']}, 总列数: {data_stats['total_columns']}</p>
                            <p>处理行数: {data_stats['sample_rows']}, 处理模式: {data_stats['processing_mode']}</p>
                        </div>
                        {html_table}
                    </body>
                    </html>
                    """
                    
                    if not MATPLOTLIB_AVAILABLE:
                        logger.warning("matplotlib不可用，使用HTML到图像的替代方案")
                        # 简化的HTML渲染方案
                        return await TableProcessingService._html_to_image_simple(html_content)
                    
                    # 使用matplotlib将HTML表格转换为图像
                    return await TableProcessingService._html_to_image(html_content, data_stats)
                        
                except Exception as e:
                    logger.error(f"处理表格文件失败: {e}")
                    raise ValueError(f"表格文件处理失败: {str(e)}")
            
            else:
                raise ValueError(f"不支持的文件类型: {file_type}")
                
        except Exception as e:
            logger.error(f"加载文件失败: {e}")
            raise ValueError(f"文件加载失败: {str(e)}")

    @staticmethod
    async def _html_to_image(html_content: str, data_stats: Dict[str, Any]) -> str:
        """
        将HTML表格转换为图像
        优化版本：支持更好的渲染质量和更大的表格
        """
        try:
            import matplotlib.pyplot as plt
            from matplotlib.patches import Rectangle
            import matplotlib.patches as mpatches
            from io import StringIO
            
            # 解析HTML表格数据
            df = pd.read_html(StringIO(html_content))[0]
            
            # 动态计算图像尺寸
            rows, cols = df.shape
            cell_width = max(1.2, min(3.0, 15.0 / cols))  # 动态调整单元格宽度
            cell_height = 0.6
            
            fig_width = max(12, cols * cell_width + 2)
            fig_height = max(8, rows * cell_height + 4)
            
            # 限制最大尺寸以避免内存问题
            fig_width = min(fig_width, 24)
            fig_height = min(fig_height, 32)
            
            fig, ax = plt.subplots(figsize=(fig_width, fig_height))
            ax.set_xlim(0, cols)
            ax.set_ylim(0, rows + 2)  # 额外空间用于标题
            ax.axis('off')
            
            # 添加标题信息
            title_text = f"数据表格 (总计: {data_stats['total_rows']}行 × {data_stats['total_columns']}列, 显示: {data_stats['sample_rows']}行)"
            ax.text(cols/2, rows + 1.5, title_text, ha='center', va='center', 
                   fontsize=12, fontweight='bold')
            
            # 绘制表格
            for i in range(rows):
                for j in range(cols):
                    # 绘制单元格边框
                    rect = Rectangle((j, rows-i-1), 1, 1, linewidth=1, 
                                   edgecolor='black', facecolor='white' if i > 0 else 'lightgray')
                    ax.add_patch(rect)
                    
                    # 添加文本内容
                    cell_value = str(df.iloc[i, j])
                    if len(cell_value) > 15:  # 截断过长的文本
                        cell_value = cell_value[:12] + "..."
                    
                    # 调整字体大小
                    fontsize = max(6, min(10, 120 / max(rows, cols)))
                    
                    ax.text(j + 0.5, rows-i-0.5, cell_value, ha='center', va='center',
                           fontsize=fontsize, fontweight='bold' if i == 0 else 'normal')
            
            # 保存为图像
            buffer = io.BytesIO()
            plt.savefig(buffer, format='PNG', dpi=150, bbox_inches='tight', 
                       facecolor='white', edgecolor='none')
            plt.close(fig)
            
            # 转换为base64
            buffer.seek(0)
            image_data = base64.b64encode(buffer.getvalue()).decode('utf-8')
            
            logger.info(f"成功将表格转换为图像，尺寸: {fig_width}x{fig_height}, 数据: {rows}x{cols}")
            return image_data
            
        except Exception as e:
            logger.error(f"HTML转图像失败: {e}")
            # 回退到简化方案
            return await TableProcessingService._html_to_image_simple(html_content)

    @staticmethod
    async def _html_to_image_simple(html_content: str) -> str:
        """
        简化的HTML到图像转换方案
        当matplotlib不可用时使用
        """
        try:
            # 提取表格数据
            df = pd.read_html(StringIO(html_content))[0]
            
            # 创建简单的文本表格图像
            rows, cols = df.shape
            
            # 使用PIL创建图像
            img_width = max(800, cols * 100)
            img_height = max(600, rows * 30 + 100)
            
            # 限制最大尺寸
            img_width = min(img_width, 2000)
            img_height = min(img_height, 2000)
            
            img = Image.new('RGB', (img_width, img_height), 'white')
            
            # 这里可以添加更复杂的文本渲染逻辑
            # 暂时返回一个简单的占位符
            buffer = io.BytesIO()
            img.save(buffer, format='PNG')
            image_data = base64.b64encode(buffer.getvalue()).decode('utf-8')
            
            logger.info(f"使用简化方案创建表格图像: {img_width}x{img_height}")
            return image_data
            
        except Exception as e:
            logger.error(f"简化HTML转图像失败: {e}")
            raise ValueError(f"无法将表格转换为图像: {str(e)}")

    @staticmethod
    async def _match_template(
        image_data: str,
        template_hint: Optional[str] = None
    ) -> TemplateMatchResult:
        """
        模板智能分流
        基于图像特征和列名匹配识别表格类型
        """
        try:
            # 如果有模板提示，直接匹配
            if template_hint and template_hint in TableProcessingService.PREDEFINED_TEMPLATES:
                return TemplateMatchResult(
                    matched=True,
                    template_id=template_hint,
                    confidence=1.0,
                    match_details={"source": "hint"}
                )
            
            # TODO: 实现基于图像的模板匹配逻辑
            # 1. 提取图像中的文本
            # 2. 分析列名和表格结构
            # 3. 与预定义模板的识别规则匹配
            
            # 暂时返回模拟匹配结果
            for template_id, template in TableProcessingService.PREDEFINED_TEMPLATES.items():
                # 简单的关键词匹配逻辑
                if template_id == "purchase_distribution":
                    return TemplateMatchResult(
                        matched=True,
                        template_id=template_id,
                        confidence=0.85,
                        match_details={"matched_rules": ["商品名称", "单价"]}
                    )
            
            return TemplateMatchResult(
                matched=False,
                confidence=0.0,
                match_details={"reason": "no_template_matched"}
            )
            
        except Exception as e:
            logger.error(f"模板匹配失败: {e}")
            return TemplateMatchResult(
                matched=False,
                confidence=0.0,
                match_details={"error": str(e)}
            )

    @staticmethod
    async def _extract_with_template(
        image_data: str,
        template_id: str,
        request: TableProcessingRequest
    ) -> TableProcessingResult:
        """
        使用规则引擎提取已知模板数据
        当前为模板模式，但仍需要AI来识别实际图片内容
        """
        try:
            template = TableProcessingService.PREDEFINED_TEMPLATES.get(template_id)
            if not template:
                raise ValueError(f"未找到模板: {template_id}")
            
            # 即使是模板模式，也需要使用AI来识别实际图片内容
            # 这里使用模板特定的提示词来指导AI更准确地提取数据
            if template_id == "purchase_distribution":
                # 构建针对采购分拨单的专用提示词
                prompt = """
请作为一个专业的表格数据分析专家，仔细观察并分析这个表格图像。

## 核心任务
请完整理解表格内容，识别其业务类型，并提取所有数据。特别注意表格的结构特征和业务逻辑。

## 分析步骤
1. **表格类型识别**：首先判断这是什么类型的业务表格
2. **结构分析**：观察表格的列结构和数据组织方式
3. **内容提取**：准确读取所有数据，包括文字、数字和结构关系

## 重要提示
- 仔细观察表格中的每一列，特别是右侧可能存在的分拨目标列
- 如果发现多个包含数字的列（除了基础商品信息列），这些通常代表不同的分拨目标
- 保持对实际列名的准确识别，不要使用通用替代词

## 输出要求
请按照以下JSON格式返回分析结果，确保数据来源于实际图像内容：

```json
{
    "success": true,
    "table_type": "实际识别的表格类型",
    "confidence": 0.95,
    "columns": ["实际的列名1", "实际的列名2", "实际的列名3", ...],
    "data": [
        {
            "列名1": "实际数据值",
            "列名2": 实际数字值,
            "列名3": "实际数据值",
            ...
        }
    ],
    "distribution_targets": ["识别到的分拨目标列名"],
    "analysis_notes": "对表格特征和识别过程的简要说明"
}
```

## 质量要求
- 所有列名必须与图像中显示的完全一致
- 数字数据保持原始精度
- 空值或无法识别的单元格标记为null
- 确保输出的JSON格式正确且完整

请开始分析这个表格图像。"""
                
                # 由于模板模式仍需要AI识别，我们返回一个需要AI处理的标记
                return TableProcessingResult(
                    success=False,
                    processing_method="template_needs_ai",
                    template_matched=template_id,
                    extracted_data=[],
                    column_mapping={},
                    ai_processing_info={"template_prompt": prompt},
                    errors=[{"type": "template_info", "message": "模板模式需要AI处理实际图片内容"}]
                )
            
            elif template_id == "inventory_sheet":
                # 构建针对库存表的专用提示词
                prompt = """
请作为一个专业的表格数据分析专家，仔细观察并分析这个表格图像。

## 核心任务
请完整理解表格内容，识别其业务类型，并提取所有数据。特别注意表格的结构特征和业务逻辑。

## 分析步骤
1. **表格类型识别**：首先判断这是什么类型的业务表格
2. **结构分析**：观察表格的列结构和数据组织方式
3. **内容提取**：准确读取所有数据，包括文字、数字和结构关系

## 重要提示
- 仔细观察表格中的每一列，特别是右侧可能存在的分拨目标列
- 如果发现多个包含数字的列（除了基础商品信息列），这些通常代表不同的分拨目标
- 保持对实际列名的准确识别，不要使用通用替代词

## 输出要求
请按照以下JSON格式返回分析结果，确保数据来源于实际图像内容：

```json
{
    "success": true,
    "table_type": "库存表",
    "confidence": 0.95,
    "columns": ["实际的列名1", "实际的列名2", "实际的列名3", ...],
    "data": [
        {
            "列名1": "实际数据值",
            "列名2": 实际数字值,
            "列名3": "实际数据值",
            ...
        }
    ],
    "distribution_targets": ["识别到的分拨目标列名"],
    "analysis_notes": "对表格特征和识别过程的简要说明"
}
```

## 质量要求
- 所有列名必须与图像中显示的完全一致
- 数字数据保持原始精度
- 空值或无法识别的单元格标记为null
- 确保输出的JSON格式正确且完整

请开始分析这个表格图像。"""
                
                # 由于模板模式仍需要AI识别，我们返回一个需要AI处理的标记
                return TableProcessingResult(
                    success=False,
                    processing_method="template_needs_ai",
                    template_matched=template_id,
                    extracted_data=[],
                    column_mapping={},
                    ai_processing_info={"template_prompt": prompt},
                    errors=[{"type": "template_info", "message": "库存表模板模式需要AI处理实际图片内容"}]
                )
            
            # 其他模板的处理
            return TableProcessingResult(
                success=False,
                processing_method="template",
                extracted_data=[],
                column_mapping={},
                errors=[{"type": "template_error", "message": f"模板 {template_id} 需要实现实际的识别逻辑"}]
            )
            
        except Exception as e:
            logger.error(f"模板提取失败: {e}")
            return TableProcessingResult(
                success=False,
                processing_method="template",
                extracted_data=[],
                column_mapping={},
                errors=[{"type": "template_error", "message": str(e)}]
            )

    @staticmethod
    async def _extract_with_ai(
        db: AsyncSession,
        image_data: str,
        request: TableProcessingRequest,
        project_id: uuid.UUID,
        tenant_id: Optional[uuid.UUID],
        user_id: uuid.UUID
    ) -> TableProcessingResult:
        """
        使用AI视觉模型提取表格数据 - 优化版本
        支持最新的视觉模型，基于系统配置的模型和参数
        """
        try:
            from services.ai.vision_service import AIVisionService
            from services.ai.system_integration_service import SystemAIIntegrationService
            
            # 获取项目和现有数据用于AI上下文
            from models.project import Project
            from sqlalchemy import select
            
            project_stmt = select(Project).where(Project.id == project_id)
            project_result = await db.execute(project_stmt)
            project = project_result.scalar_one_or_none()
            
            if not project:
                raise ValueError(f"项目不存在: {project_id}")
            
            # 加载项目现有数据作为上下文
            existing_data = await TableProcessingService._load_project_context(db, project_id)
            
            # 使用系统AI整合配置获取默认视觉模型
            integration_config = await SystemAIIntegrationService.get_integration_config(
                db=db,
                project_id=project_id
            )
            
            if not integration_config:
                raise ValueError("未找到系统AI整合配置")
                
            # 获取默认视觉模型ID
            default_vision_model_id = None
            if isinstance(integration_config, dict):
                default_vision_model_id = integration_config.get("default_vision_model_id")
            else:
                default_vision_model_id = integration_config.default_vision_model_id
                
            if not default_vision_model_id:
                raise ValueError("系统整合配置中未设置默认视觉模型")
            
            # 使用系统配置的参数，避免硬编码
            ai_config = {
                "temperature": request.vision_temperature or 0.1,
                "max_tokens": 16384  # 使用合理的默认值
            }
            config_strategy = "system_config"
            logger.info(f"使用系统配置参数: temperature={ai_config['temperature']}, max_tokens={ai_config['max_tokens']}")
            
            # 智能检测表格类型和生成优化的提示词
            smart_prompt = await TableProcessingService._generate_smart_prompt(
                request, existing_data, image_data
            )
            
            logger.info(f"生成的智能提示词长度: {len(smart_prompt)} 字符")
            
            # NEW logger line
            logger.info(
                f"调用AI视觉服务: model_id={default_vision_model_id}, "
                f"prompt_length={len(smart_prompt)}, "
                f"temperature={request.vision_temperature or ai_config.get('temperature', 0.1)}, "
                f"max_tokens={ai_config.get('max_tokens', 8192)}, "
                f"additional_messages_count={len(request.additional_messages) if request.additional_messages else 0}"
            )
            # 调用AI服务 - 使用系统配置的模型和参数
            ai_response = await AIVisionService.analyze_image(
                db=db,
                project_id=project_id,
                user_id=user_id,
                image_data=image_data,
                prompt=smart_prompt,
                model_id=default_vision_model_id,  # 使用系统配置的默认视觉模型
                temperature=request.vision_temperature or ai_config.get("temperature"),
                max_tokens=ai_config.get("max_tokens"),
                additional_messages=request.additional_messages
            )
            
            if not ai_response:
                raise ValueError("AI分析失败: 未收到响应")
            
            logger.info(f"AI响应类型: {type(ai_response)}")
            logger.info(f"AI响应内容（前500字符）: {str(ai_response)[:500]}")
            
            # 解析AI响应 - 增强版本
            parsed_data_dict: Dict[str, Any] = {} # Initialize
            raw_ai_assistant_content_str: str = "" # Initialize
            try:
                parsed_data_dict, raw_ai_assistant_content_str = await TableProcessingService._parse_ai_response_enhanced(
                    ai_response, request, existing_data
                )
                logger.info(f"解析后的数据类型 (parsed_data_dict): {type(parsed_data_dict)}")
                logger.info(f"解析后的数据keys: {parsed_data_dict.keys() if isinstance(parsed_data_dict, dict) else 'not dict'}")
                logger.info(f"原始AI助手内容字符串 (raw_ai_assistant_content_str) 前100字符: {raw_ai_assistant_content_str[:100]}")
                
                # 检查是否提取到数据
                has_data = False
                data_info = ""

                if parsed_data_dict and parsed_data_dict.get("data") and isinstance(parsed_data_dict.get("data"), list):
                    data_list = parsed_data_dict.get("data", [])
                    has_data = True
                    data_info = f"提取到数据记录: {len(data_list)}条"
                elif isinstance(parsed_data_dict, dict) and not parsed_data_dict.get("data") and parsed_data_dict:
                    has_data = True
                    data_info = f"提取到字典数据，键数量: {len(parsed_data_dict)}"
                elif isinstance(parsed_data_dict, list) and parsed_data_dict:
                    has_data = True
                    data_info = f"提取到列表数据，行数: {len(parsed_data_dict)}"

                if has_data:
                    logger.info(data_info)
                else:
                    logger.warning(f"未从解析结果中识别出明确的数据结构: {type(parsed_data_dict)}")
                    
            except Exception as parse_error:
                logger.error(f"解析AI响应失败: {parse_error}", exc_info=True) # Add exc_info
                # Attempt to build ai_info_content even on parse failure, at least with prompt
                ai_info_on_error = {
                    "error_details": f"解析AI响应失败: {parse_error}",
                    "error_type": type(parse_error).__name__,
                    "strategy": config_strategy if 'config_strategy' in locals() else "unknown",
                    "effective_prompt": smart_prompt if 'smart_prompt' in locals() else "unknown prompt"
                }
                raise ValueError(f"解析AI响应失败: {parse_error}") from parse_error
            
            # 首先进行数据标准化和验证
            standardized_result = await TableProcessingService._standardize_extracted_data_enhanced(
                parsed_data_dict, request, existing_data # Pass parsed_data_dict
            )
            
            # ... (validation of standardized_result remains the same)
            if not standardized_result or not standardized_result.get("data"):
                logger.error(f"Standardization failed to produce valid data. Standardized result: {standardized_result}")
                logger.error(f"Original parsed_data_dict from AI: {parsed_data_dict}")
                raise ValueError(f"AI未能提取到有效的表格数据，解析/标准化结果: {standardized_result}")
            
            logger.info(f"AI提取完成，识别到 {len(standardized_result['data'])} 行数据")

            ai_info_content = {
                "table_type": standardized_result.get('table_type', '通用表格'),
                "confidence": standardized_result.get('confidence', 0.0),
                "strategy": config_strategy if 'config_strategy' in locals() else "default",
                "data_quality": standardized_result.get('data_quality', {}),
                "processing_notes": standardized_result.get('processing_notes', '')
            }
            
            result = TableProcessingResult(
                success=True,
                processing_method="ai_vision_enhanced",
                template_matched=None,
                extracted_data=standardized_result['data'],
                column_mapping=standardized_result.get('column_mapping', {}),
                total_rows=len(standardized_result['data']),
                valid_rows=standardized_result.get('valid_rows', len(standardized_result['data'])),
                error_rows=standardized_result.get('error_rows', 0),
                errors=standardized_result.get('errors', []),
                warnings=standardized_result.get('warnings', []),
                ai_processing_info=ai_info_content
            )
            
            return result
            
        except Exception as e:
            error_msg = str(e) if str(e) else f"未知错误: {type(e).__name__}"
            logger.error(f"AI表格提取失败(outer try-except): {error_msg}", exc_info=True)
            
            # Populate ai_info_content for error case
            ai_info_on_error = {
                "error_details": error_msg,
                "error_type": type(e).__name__,
                "strategy": config_strategy if 'config_strategy' in locals() else "unknown",
                "effective_prompt": smart_prompt if 'smart_prompt' in locals() else "unknown prompt"
            }
            # If raw_ai_assistant_content_str was defined before exception, include it
            if 'raw_ai_assistant_content_str' in locals() and raw_ai_assistant_content_str:
                 ai_info_on_error["raw_ai_assistant_content_for_error"] = raw_ai_assistant_content_str[:1000] # Limit length

            return TableProcessingResult(
                success=False,
                processing_method="ai_vision_enhanced",
                extracted_data=[],
                column_mapping={},
                errors=[{"type": "ai_processing_error", "message": error_msg}],
                ai_processing_info=ai_info_on_error
            )

    @staticmethod
    async def _generate_smart_prompt(
        request: TableProcessingRequest,
        existing_data: Dict[str, Any], # 项目中已有的商品、分类、供应商等数据
        image_data: str # 图像的base64数据，可选，用于更复杂的分析
    ) -> str:
        """
        根据用户输入和潜在的表格类型生成更智能、更优化的AI提示词
        """
        # If a custom_prompt is provided by the user, use it directly and bypass smart generation.
        if request.custom_prompt and request.custom_prompt.strip(): # Check if not None and not empty/whitespace
            logger.info("使用用户提供的 custom_prompt，跳过智能提示词生成。")
            return request.custom_prompt

        # Original logic starts here if no custom_prompt is given or if it's empty
        base_prompt = request.custom_prompt or "提取表格中的所有数据。" # This line is now effectively: base_prompt = "提取表格中的所有数据。" if custom_prompt was None/empty
        
        potential_table_type = "通用表格" 
        
        prompt_lower = base_prompt.lower()
        if "采购分拨单" in base_prompt or "分拨" in base_prompt: # This will now only evaluate if no custom_prompt was given
            potential_table_type = "采购分拨单"
        elif "库存" in prompt_lower:
            potential_table_type = "库存表"
        elif "采购单" in base_prompt or "采购订单" in base_prompt:
            potential_table_type = "采购单"
        elif "盘点" in prompt_lower:
            potential_table_type = "盘点表"

        # The rest of the original logic for auto-generating prompts based on potential_table_type
        # will now only execute if no valid custom_prompt was provided.
        if potential_table_type == "采购分拨单":
            logger.info('检测到可能是"采购分拨单"，使用通用分拨单提示词。')
            specific_instructions = """
请分析这张采购分拨单表格，提取所有数据。

采购分拨单通常包含：
- 商品基础信息（商品名称、单位、规格、单价、合计、商品分类、商品品牌等）
- 分拨目标信息（门店、仓库等）
- 分拨数量和金额

请返回JSON格式：
{
  "table_type": "采购分拨单",
  "data": [
    {
      "商品名称": "商品名称",
      "单位": "单位",
      "规格": "规格数字",
      "单价": 数字,
      "门店名称": "门店名称",
      "数量": 数字,
      "金额": 数字
    }
  ]
}

要求：
1. 准确识别表格结构和数据
2. 数字字段必须是纯数字
3. 提取所有有效的分拨记录
4. 保持数据的完整性和准确性
"""
            smart_prompt = specific_instructions
        
        elif potential_table_type == "库存表":
            logger.info('检测到可能是"库存表"，使用优化的提示词。')
            smart_prompt = """
提取库存表格的所有数据。

JSON格式：
{
  "table_type": "库存表",
  "data": [
    {
      "商品名称": "商品名称",
      "商品编码": "编码",
      "规格": "规格数字",
      "单位": "单位",
      "库存数量": 数量,
      "成本价": 价格,
      "库存金额": 金额,
      "仓库": "仓库名称"
    }
  ]
}

要求：提取所有商品的库存信息，确保数据完整。
"""
        elif potential_table_type == "采购单":
            logger.info('检测到可能是"采购单"，使用优化的提示词。')
            smart_prompt = """
提取采购单表格的所有数据。

JSON格式：
{
  "table_type": "采购单", 
  "data": [
    {
      "商品名称": "商品名称",
      "商品编码": "编码",
      "规格": "规格数字",
      "单位": "单位",
      "采购数量": 数量,
      "采购单价": 价格,
      "采购金额": 金额,
      "供应商": "供应商名称"
    }
  ]
}

要求：提取所有采购商品信息，确保数量和金额准确。
"""
        elif potential_table_type == "盘点表":
            logger.info('检测到可能是"盘点表"，使用优化的提示词。')
            smart_prompt = """
提取盘点表格的所有数据。

JSON格式：
{
  "table_type": "盘点表",
  "data": [
    {
      "商品名称": "商品名称", 
      "商品编码": "编码",
      "规格": "规格数字",
      "单位": "单位",
      "账面数量": 数量,
      "实盘数量": 数量,
      "盈亏数量": 数量,
      "成本价": 价格,
      "盈亏金额": 金额
    }
  ]
}

要求：提取所有盘点商品信息，准确计算盈亏。
"""
        else: # 通用表格
            # 检查用户是否已经提供了详细的JSON结构示例
            if "\"data\":" in base_prompt and "[" in base_prompt and "{" in base_prompt:
                # 用户可能已经提供了详细的格式，直接使用
                logger.info("用户提示中可能包含详细JSON结构，优先使用用户提示。")
                smart_prompt = base_prompt 
            else:
                logger.info("使用通用的表格提取提示词。")
                smart_prompt = f"""
{base_prompt}

请以JSON格式返回提取的数据，格式如下：
{{
  "table_type": "通用表格",
  "data": [
    {{
      "列名1": "值1",
      "列名2": "值2"
      // ... 根据表格实际列名和数据填充 ...
    }}
    // ... 更多行 ...
  ]
}}
确保提取表格中的每一行和每一列数据。
"""

        # 添加对现有数据的引用，帮助AI更好地理解上下文（如果需要）
        # if existing_data:
        #     smart_prompt += "\n\n作为参考，以下是系统中已存在的一些相关数据：\n"
        #     if existing_data.get('products'):
        #         smart_prompt += f"商品列表: {json.dumps(existing_data['products'][:5], ensure_ascii=False, indent=2)}\n"
        #     if existing_data.get('categories'):
        #         smart_prompt += f"商品分类: {json.dumps(existing_data['categories'][:5], ensure_ascii=False, indent=2)}\n"

        # logger.debug(f"生成的智能提示词: {smart_prompt}")
        return smart_prompt

    @staticmethod
    async def _parse_ai_response_enhanced(
        ai_response: Dict[str, Any],
        request: TableProcessingRequest,
        existing_data: Dict[str, Any]
    ) -> Tuple[Dict[str, Any], str]: # MODIFIED: Return type changed to Tuple
        """
        增强版AI响应解析. Returns parsed data and raw AI content string.
        """
        ai_content_str = "" # Initialize raw AI content string
        
        # OpenAI/Azure/OpenRouter/阿里巴巴百炼格式
        if "choices" in ai_response and ai_response["choices"]:
            choice = ai_response["choices"][0]
            if "message" in choice and "content" in choice["message"]:
                ai_content_str = choice["message"]["content"]
        # Anthropic格式
        elif "content" in ai_response and ai_response["content"]:
            if isinstance(ai_response["content"], list) and ai_response["content"]:
                ai_content_str = ai_response["content"][0].get("text", "")
            elif isinstance(ai_response["content"], str):
                ai_content_str = ai_response["content"]
        # Dashscope (通义千问) - 假设它可能在 output.text 或类似结构
        elif "output" in ai_response and isinstance(ai_response["output"], dict) and "text" in ai_response["output"]:
            ai_content_str = ai_response["output"]["text"]
            # Qwen-VL-Max有时会在output.choices[0].message.content
            if not ai_content_str and "choices" in ai_response["output"] and isinstance(ai_response["output"]["choices"], list) and ai_response["output"]["choices"]: 
                qwen_choice = ai_response["output"]["choices"][0]
                if "message" in qwen_choice and isinstance(qwen_choice["message"], dict) and "content" in qwen_choice["message"]:
                    ai_content_str = qwen_choice["message"]["content"]

        if not ai_content_str:
            # Fallback or if the structure is directly the content string (less common for complex responses)
            if isinstance(ai_response, str):
                ai_content_str = ai_response # Should ideally not happen if ai_response is Dict
            else: # Log a warning if content is still not found from expected structures
                logger.warning(f"AI响应中未按已知结构找到有效内容。AI Response Keys: {list(ai_response.keys() if isinstance(ai_response, dict) else [])}")
                # Attempt to stringify the whole response as a last resort, might be noisy
                ai_content_str = json.dumps(ai_response) if isinstance(ai_response, dict) else str(ai_response)
                # But if it's a dict, we expect the content to be inside, so an empty string is safer if not found
                if isinstance(ai_response, dict):
                    ai_content_str = "" # Reset if it was a dict and content not found via specific paths

        # If after all attempts, ai_content_str is still effectively empty, raise error or return empty.
        if not ai_content_str.strip():
            logger.error(f"解析后AI内容字符串为空或无效. Original ai_response: {str(ai_response)[:500]}")
            # Depending on strictness, either raise or return empty data + empty string
            # For now, let downstream parsing attempt with this (potentially empty) string
            # raise ValueError("AI响应中未找到或提取到有效内容字符串") 

        logger.info(f"AI响应内容长度 (ai_content_str): {len(ai_content_str)}")
        logger.info(f"AI响应前500字符 (ai_content_str): {ai_content_str[:500]}")
        
        # ... (token usage logging remains the same) ...
        if "usage" in ai_response:
            usage = ai_response["usage"]
            completion_tokens = usage.get("completion_tokens", 0)
            total_tokens = usage.get("total_tokens", 0)
            
            logger.info(f"Token使用情况 - 输入: {usage.get('prompt_tokens', 0)}, "
                       f"输出: {completion_tokens}, 总计: {total_tokens}")
            
            if completion_tokens >= 8000:
                logger.warning("输出token接近上限，响应可能被截断")
            elif completion_tokens >= 6000:
                logger.warning("输出token较高，建议优化提示词")

        extracted_data_obj: Dict[str, Any] = {} # Default to empty dict
        # 策略1: 直接解析整个响应 (using ai_content_str)
        try:
            extracted_data_obj = json.loads(ai_content_str)
            logger.info("直接解析AI内容字符串成功")
            return extracted_data_obj, ai_content_str # MODIFIED: Return tuple
        except json.JSONDecodeError:
            logger.info("直接解析失败，尝试提取JSON部分")
        
        # 策略2: 多种JSON提取模式 (from ai_content_str)
        json_patterns = [
            r'```json\s*(\{.*?\})\s*```',
            r'```\s*(\{.*?\})\s*```',
            r'(\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\})',
            r'(\{.*?\})',
        ]
        
        json_extracted_from_regex = None
        for pattern in json_patterns:
            match = re.search(pattern, ai_content_str, re.DOTALL)
            if match:
                json_extracted_from_regex = match.group(1)
                logger.info(f"使用模式提取到JSON: {pattern}")
                break
        
        if not json_extracted_from_regex:
            json_extracted_from_regex = ai_content_str.strip()
            logger.info("未找到JSON块，使用整个AI内容字符串进行修复尝试")
        
        # 策略3: 使用json-repair库修复JSON (using json_extracted_from_regex)
        try:
            import json_repair
            logger.info("使用json-repair库修复JSON")
            # logger.info(f"待修复的JSON内容: {json_extracted_from_regex[:500]}")
            
            extracted_data_obj = json_repair.loads(json_extracted_from_regex)
            logger.info(f"json-repair修复成功，结果类型: {type(extracted_data_obj)}")
            # logger.info(f"json-repair修复结果: {str(extracted_data_obj)[:500]}")
            return extracted_data_obj, ai_content_str # MODIFIED: Return tuple
            
        except ImportError:
            logger.warning("json-repair库未安装，使用内置修复逻辑")
            repaired_data_obj = TableProcessingService._fallback_json_repair(json_extracted_from_regex)
            return repaired_data_obj, ai_content_str # MODIFIED: Return tuple
            
        except Exception as e:
            logger.error(f"json-repair修复失败: {e}")
            logger.error(f"待修复的JSON内容 (json_extracted_from_regex): {json_extracted_from_regex}")
            repaired_data_obj = TableProcessingService._fallback_json_repair(json_extracted_from_regex)
            return repaired_data_obj, ai_content_str # MODIFIED: Return tuple

    @staticmethod
    async def _standardize_extracted_data_enhanced(
        extracted_data: Dict[str, Any],
        request: TableProcessingRequest,
        existing_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        增强版数据标准化和验证
        """
        # 验证基础数据结构
        if not isinstance(extracted_data, dict) and not isinstance(extracted_data, list): # Allow direct list input as well
            raise ValueError(f"提取的数据不是有效的字典或列表格式: {type(extracted_data)}")
        
        # 获取数据行 - 兼容多种格式
        data_rows = []
        
        # 格式0 (新): 处理以商品名为键的顶层对象 {'商品A':{...}, '商品B':{...}}
        # 检查 extracted_data 是否为字典，其值也都是字典，并且顶层没有 "data" 键 (用以区分其他格式)
        # 并且字典本身不为空
        if (isinstance(extracted_data, dict) and
            not "data" in extracted_data and
            extracted_data and
            all(isinstance(v, dict) for v in extracted_data.values())):
            
            logger.info("检测到以商品名为键的顶层对象结构，开始转换。")
            temp_rows = []
            for product_name, product_details in extracted_data.items():
                if isinstance(product_details, dict): # 再次确认商品详情是字典
                    row = {"商品名称": product_name} # 创建新行，并设置商品名称
                    row.update(product_details)    # 合并商品详情
                    temp_rows.append(row)
                else:
                    logger.warning(f"商品 '{product_name}' 的值不是预期的字典类型: {type(product_details)}，已跳过。")
            
            if temp_rows:
                data_rows = temp_rows
                logger.info(f"成功将商品名键控对象转换为 {len(data_rows)} 行标准数据。")
            else:
                logger.warning("商品名键控对象转换后未产生有效数据行（可能所有值都不是字典或原字典为空）。")
        
        # 格式0.5 (新增): 处理门店分拨格式 {'万达': [...], '南塘': [...]}
        # 检查是否所有值都是列表，且列表中包含商品信息字典
        elif (isinstance(extracted_data, dict) and
              not "data" in extracted_data and
              extracted_data and
              all(isinstance(v, list) for v in extracted_data.values()) and
              # 进一步检查列表中是否包含商品信息字典
              any(isinstance(item, dict) and any(key in item for key in ["商品名称", "数量", "单价", "规格"]) 
                  for store_items in extracted_data.values() for item in store_items if isinstance(item, dict))):
            
            logger.info("检测到门店分拨格式 {'门店名': [商品列表]}，开始转换。")
            temp_rows = []
            for store_name, products in extracted_data.items():
                if isinstance(products, list):
                    for product in products:
                        if isinstance(product, dict):
                            row = product.copy()  # 复制商品信息
                            row["门店名称"] = store_name  # 添加门店名称
                            temp_rows.append(row)
                        else:
                            logger.warning(f"门店 '{store_name}' 中的商品不是字典格式: {type(product)}")
                else:
                    logger.warning(f"门店 '{store_name}' 的值不是列表格式: {type(products)}")
            
            if temp_rows:
                data_rows = temp_rows
                logger.info(f"成功将门店分拨格式转换为 {len(data_rows)} 行标准数据。")
            else:
                logger.warning("门店分拨格式转换后未产生有效数据行。")
        
        # 格式1: {"table_type": "...", "data": [...]}
        elif "data" in extracted_data and isinstance(extracted_data.get("data"), list): # Use .get for safety
            logger.info("检测到标准 {\"data\": [...]} 列表结构。")
            data_rows = extracted_data["data"]
        # 格式2: {"data": {...}} (无table_type, data是单个字典)
        elif "data" in extracted_data and isinstance(extracted_data.get("data"), dict): # Use .get for safety
            logger.info("检测到 {\"data\": {...}} 单对象结构，包装为列表。")
            data_rows = [extracted_data["data"]]
        # 格式3: 直接返回单行数据字典或包含门店的分拨数据 (通常是扁平的, 不含 "data" 键)
        elif isinstance(extracted_data, dict) and not "data" in extracted_data and any(key in str(extracted_data.keys()) for key in ["商品", "名称", "数量", "价格", "单价", "规格", "单位"]):
            logger.info("检测到扁平字典结构（可能为单行数据或待拆分的分拨数据）。")
            # 检查是否是分拨单格式（包含门店分配数据）
            # Fields that describe the product or are general summary, not store names.
            # The AI prompt guides it to use actual store names as keys for quantities.
            product_or_summary_fields = {
                "商品名称", "单位", "规格", "单价", "合计", "总计", "总金额", 
                "商品分类", "商品编码", "品牌", 
                "数量", "金额", "毛利率", "税率", "税额" # Added common summary/numeric fields
            }
            store_fields = []
            potential_store_data = {} # Store identified store keys and their values

            for key, value in extracted_data.items():
                # A key is a potential store if it's NOT a product/summary field,
                # AND its value suggests it's a quantity (numeric and > 0).
                if key not in product_or_summary_fields:
                    try:
                        # Attempt to convert value to float, handling strings like "1,234.56" or "10"
                        str_value = str(value).replace(',', '') # Remove commas for parsing
                        num_val = float(str_value)
                        if num_val > 0: # Consider only positive quantities for stores
                            store_fields.append(key)
                            potential_store_data[key] = num_val
                        # else: logger.debug(f"Field '{key}' numeric but not >0: {num_val}")
                    except (ValueError, TypeError):
                        # Value is not a number, so 'key' is unlikely a store quantity column
                        # logger.debug(f"Field '{key}' value '{value}' not numeric, not a store.")
                        pass 
                # else: logger.debug(f"Field '{key}' is in product_or_summary_fields.")
            
            logger.info(f"Identified potential store_fields: {store_fields} from extracted_data keys: {list(extracted_data.keys())}")
            logger.debug(f"Potential store data: {potential_store_data}")

            # ... rest of the Format 3 logic, ensuring it uses `potential_store_data` for quantities ...
            # (The misidentification of '单价' logic might need adjustment if '单价' is in product_or_summary_fields)
            # The existing logic for '单价' validation seems okay to keep for now.
            unit_price = extracted_data.get("单价", 0)
            # ... (original unit_price validation and correction logic remains largely the same, but ensure it doesn't conflict with store_fields)
            # Make sure that if a field was corrected to be '单价', it's removed from store_fields if it was accidentally added.
            # This part is complex; the main fix is the robust `product_or_summary_fields`.
            
            if store_fields: # If we identified any actual store_fields
                data_rows = []
                base_info = {
                    "商品名称": extracted_data.get("商品名称", ""),
                    "单位": extracted_data.get("单位", ""),
                    "规格": extracted_data.get("规格", ""),
                    "单价": extracted_data.get("单价", 0), # This should be correct now or a number
                    "商品分类": extracted_data.get("商品分类", ""),
                    "商品编码": extracted_data.get("商品编码", "")
                }
                
                try:
                    base_info["单价"] = float(base_info["单价"]) if base_info["单价"] is not None and str(base_info["单价"]).strip() != "" else 0
                except (ValueError, TypeError):
                    logger.warning(f"Base info单价转换失败 for val '{base_info['单价']}', using 0")
                    base_info["单价"] = 0
                
                for store_name in store_fields:
                    quantity_num = potential_store_data.get(store_name) # Get quantity from our validated numeric dict
                    if quantity_num and quantity_num > 0:
                        row = base_info.copy()
                        row["门店名称"] = store_name
                        row["数量"] = quantity_num
                        row["金额"] = base_info["单价"] * quantity_num
                        data_rows.append(row)
                    else:
                        logger.debug(f"Skipping store {store_name} due to zero or missing quantity in potential_store_data.")
            else:
                # 普通单行数据
                data_rows = [extracted_data]
                logger.info("检测到单行数据格式，转换为列表")
        # 格式4: 直接是列表
        elif isinstance(extracted_data, list):
            logger.info("检测到直接的列表输入结构。")
            data_rows = extracted_data
            
        # logger.debug(f"标准化前的数据行 (data_rows): {data_rows}")

        if not data_rows and isinstance(extracted_data, dict) and not extracted_data.get("data"):
             # Fallback for a simple flat dictionary that wasn't caught by Format 0 or Format 3 (e.g. AI returned just one product's flat dict)
             # And it's not our Product-keyed object structure (that would be caught by Format 0)
             # And it doesn't have store-like keys (that would be Format 3 complex logic)
             # This is a basic "flat dict is one row" assumption
             is_product_keyed_format = all(isinstance(v, dict) for v in extracted_data.values()) if extracted_data else False
             if not is_product_keyed_format: # Only if not already handled by Format 0
                logger.info("No data_rows yet, and extracted_data is a simple flat dict. Assuming it's a single row.")
                data_rows = [extracted_data]


        # 验证数据行是否被填充
        if not data_rows:
            # 改进错误日志，包含更多上下文
            logger.error(f"关键错误：在尝试所有已知格式后，未能从 AI 提取的原始数据中解析出任何数据行。")
            logger.error(f"AI原始提取数据 (extracted_data): {json.dumps(extracted_data, ensure_ascii=False, indent=2)}") # 更易读的JSON输出
            logger.error(f"extracted_data 类型: {type(extracted_data)}")
            if isinstance(extracted_data, dict):
                logger.error(f"extracted_data 顶层键: {list(extracted_data.keys())}")
                if "data" in extracted_data:
                    logger.error(f"'data' 键存在，类型为: {type(extracted_data['data'])}")
            
            raise ValueError(f"未找到有效数据行或无法转换AI提供的数据结构。原始AI输出 (部分): {str(extracted_data)[:500]}")

        # 后续处理 (保持不变)
        processed_rows = []

        # 数据质量统计
        quality_stats = {
            "total_rows": len(data_rows),
            "valid_rows": 0,
            "error_rows": 0,
            "empty_rows": 0,
            "field_completeness": {},
            "data_types": {}
        }
        
        # 标准化数据
        standardized_data = []
        errors = []
        warnings = []
        
        # 分析第一行确定字段结构
        if data_rows:
            first_row = data_rows[0]
            if isinstance(first_row, dict):
                for field in first_row.keys():
                    quality_stats["field_completeness"][field] = 0
                    quality_stats["data_types"][field] = set()
        
        for i, row in enumerate(data_rows):
            try:
                if not isinstance(row, dict):
                    errors.append({
                        "row": i + 1,
                        "type": "invalid_format",
                        "message": f"第{i+1}行不是字典格式"
                    })
                    quality_stats["error_rows"] += 1
                    continue
                
                # 检查是否为空行
                if not any(str(v).strip() for v in row.values() if v is not None):
                    quality_stats["empty_rows"] += 1
                    continue
                
                # 标准化单行数据
                standardized_row = {}
                row_has_data = False
                
                for key, value in row.items():
                    # 标准化字段名
                    clean_key = str(key).strip() if key else f"字段_{len(standardized_row)}"
                    
                    # 标准化字段值 - 增加更严格的验证
                    clean_value = TableProcessingService._standardize_field_value(
                        clean_key, value, quality_stats
                    )
                    
                    standardized_row[clean_key] = clean_value
                    
                    # 统计字段完整性
                    if clean_value not in [None, "", 0]:
                        quality_stats["field_completeness"][clean_key] += 1
                        row_has_data = True
                    
                    # 记录数据类型
                    quality_stats["data_types"][clean_key].add(type(clean_value).__name__)
                
                if row_has_data:
                    # 添加标准字段
                    TableProcessingService._add_standard_fields(standardized_row, existing_data)
                    
                    standardized_data.append(standardized_row)
                    quality_stats["valid_rows"] += 1
                else:
                    quality_stats["empty_rows"] += 1
                    
            except Exception as e:
                errors.append({
                    "row": i + 1,
                    "type": "processing_error",
                    "message": f"处理第{i+1}行时出错: {str(e)}"
                })
                quality_stats["error_rows"] += 1
                logger.error(f"处理第{i+1}行数据时出错: {e}", exc_info=True)
        
        # 计算字段完整性百分比
        for field, count in quality_stats["field_completeness"].items():
            if quality_stats["valid_rows"] > 0:
                quality_stats["field_completeness"][field] = round(
                    (count / quality_stats["valid_rows"]) * 100, 2
                )
        
        # 转换数据类型集合为列表
        for field, types in quality_stats["data_types"].items():
            quality_stats["data_types"][field] = list(types)
        
        # 数据质量检查
        if quality_stats["valid_rows"] == 0:
            raise ValueError("没有有效的数据行")
        
        if quality_stats["error_rows"] > quality_stats["valid_rows"] * 0.5:
            warnings.append({
                "type": "data_quality_warning",
                "message": f"错误行数({quality_stats['error_rows']})超过有效行数的50%"
            })
        
        # 构建列映射
        column_mapping = {}
        if standardized_data:
            for key in standardized_data[0].keys():
                column_mapping[key] = key
        
        # 根据有效数据量调整成功标识和日志
        success_message = f"成功处理{quality_stats['valid_rows']}行"
        if quality_stats['error_rows'] > 0:
            success_message += f"，错误{quality_stats['error_rows']}行"
        if quality_stats['empty_rows'] > 0:
            success_message += f"，空行{quality_stats['empty_rows']}行"
            
        logger.info(success_message)
        
        return {
            "data": standardized_data,
            "column_mapping": column_mapping,
            "table_type": extracted_data.get("table_type", "通用表格"),
            "confidence": min(0.9, quality_stats["valid_rows"] / quality_stats["total_rows"]),
            "valid_rows": quality_stats["valid_rows"],
            "error_rows": quality_stats["error_rows"],
            "errors": errors,
            "warnings": warnings,
            "data_quality": quality_stats,
            "processing_notes": success_message
        }

    @staticmethod
    def _standardize_field_value(field_name: str, value: Any, quality_stats: Dict) -> Any:
        """
        标准化字段值 - 优化版本，正确处理规格字段
        """
        if value is None:
            return ""

        # 数字字段处理 - 明确区分数量/金额字段和规格字段
        numeric_fields = [
            "单价", "价格", "金额", "数量", "合计", "总计", "成本", "库存",
            "采购数量", "采购单价", "采购金额", "库存数量", "库存金额",
            "账面数量", "实盘数量", "盈亏数量", "盈亏金额", "分拨数量", "分拨金额"
        ]

        # 规格字段应该保持为文本，不强制转换为数字
        spec_fields = ["规格", "specification", "spec", "型号", "包装规格"]

        is_numeric_field = any(nf in field_name for nf in numeric_fields)
        is_spec_field = any(sf in field_name for sf in spec_fields)

        if is_spec_field:
            # 规格字段保持原始文本格式
            return str(value).strip() if value else ""
        elif is_numeric_field:
            # 数量/金额字段转换为数字
            if isinstance(value, (int, float)):
                return float(value) if value != int(value) else int(value)

            if isinstance(value, str):
                # 清理数字字符串
                import re
                cleaned_value = re.sub(r'[^\d.-]', '', str(value))
                if cleaned_value:
                    try:
                        num_value = float(cleaned_value)
                        return int(num_value) if num_value == int(num_value) else num_value
                    except ValueError:
                        pass

            return 0  # 数字字段默认为0

        # 其他文本字段处理
        text_value = str(value).strip() if value else ""

        # 特殊处理门店名称字段，清理可能的格式问题
        if "门店" in field_name or "店铺" in field_name or field_name in ["门店名称", "store_name"]:
            # 清理门店名称中的特殊字符
            import re
            text_value = re.sub(r'[^\u4e00-\u9fff\w\s]', '', text_value)  # 保留中文、字母、数字、空格
            text_value = text_value.strip()

        return text_value



    @staticmethod
    def _add_standard_fields(row: Dict[str, Any], existing_data: Dict[str, Any]) -> None:
        """
        添加标准字段
        """
        # 添加商品分类推断
        if "商品分类" not in row or not row["商品分类"]:
            if "商品名称" in row and row["商品名称"]:
                row["suggested_category"] = TableProcessingService._infer_category(
                    row["商品名称"], existing_data
                )
        
        # 添加商品品牌推断
        if "商品品牌" not in row or not row["商品品牌"]:
            if "商品名称" in row and row["商品名称"]:
                row["suggested_brand"] = TableProcessingService._infer_brand(
                    row["商品名称"], existing_data
                )

    @staticmethod
    def _infer_category(product_name: str, existing_data: Dict[str, Any]) -> str:
        """
        推断商品分类
        """
        if not product_name:
            return ""
        
        name_lower = product_name.lower()
        
        # 简单的分类推断规则
        category_keywords = {
            "水果": ["杨梅", "樱桃", "苹果", "枇杷", "番茄", "桃", "荔枝", "蓝莓", "葡萄", "瓜", "西瓜", "猕猴桃", "山竹", "榴莲", "椰", "牛油果", "香蕉"],
            "蔬菜": ["青瓜", "萝卜", "白菜", "菠菜", "茄子", "土豆", "洋葱"],
            "肉类": ["鸡翅", "猪肉", "牛肉", "羊肉", "鱼", "虾"],
            "零食": ["瓜子", "坚果", "薯片", "饼干"],
            "饮料": ["可乐", "果汁", "茶", "咖啡", "水"]
        }
        
        for category, keywords in category_keywords.items():
            if any(keyword in name_lower for keyword in keywords):
                return category
        
        return "其他"

    @staticmethod  
    def _infer_brand(product_name: str, existing_data: Dict[str, Any]) -> str:
        """
        推断商品品牌
        """
        if not product_name:
            return ""
        
        # 简单的品牌推断规则
        brand_keywords = {
            "佳沛": ["佳沛"],
            "新西兰佳沛": ["佳沛金果", "佳沛绿果"],
            "大丰": ["大丰"],
            "怡颗莓": ["怡颗莓"]
        }
        
        name_lower = product_name.lower()
        for brand, keywords in brand_keywords.items():
            if any(keyword in product_name for keyword in keywords):
                return brand
        
        return ""

    @staticmethod
    def register_custom_template(template: TableTemplate):
        """注册自定义模板"""
        TableProcessingService.PREDEFINED_TEMPLATES[template.template_id] = template

    @staticmethod
    def get_available_templates() -> List[TableTemplate]:
        """获取所有可用模板"""
        return list(TableProcessingService.PREDEFINED_TEMPLATES.values())

    @staticmethod
    async def _load_project_context(db: AsyncSession, project_id: uuid.UUID) -> Dict[str, Any]:
        """
        加载项目现有数据作为AI上下文
        """
        try:
            # 获取现有商品分类和品牌信息
            from services.product_service import ProductService
            existing_data = await ProductService.get_existing_categories_and_brands(db, project_id)
            logger.info(f"获取到现有分类 {len(existing_data.get('categories', []))} 个，品牌 {len(existing_data.get('brands', []))} 个")
            return existing_data
        except Exception as e:
            logger.warning(f"获取现有分类品牌失败: {e}")
            return {"categories": [], "brands": []}

    @staticmethod
    def _fallback_json_repair(json_content: str) -> Dict[str, Any]:
        """
        内置JSON修复逻辑
        """
        import json
        import re
        
        try:
            # 尝试基本清理
            cleaned_content = json_content.strip()
            
            # 移除可能的多余字符
            if cleaned_content.startswith('```json'):
                cleaned_content = re.sub(r'^```json\s*', '', cleaned_content)
            if cleaned_content.endswith('```'):
                cleaned_content = re.sub(r'\s*```$', '', cleaned_content)
            
            # 处理常见的JSON错误
            # 1. 修复未闭合的引号
            cleaned_content = re.sub(r'([^"\\])"([^",:}\]]*?)$', r'\1"\2"', cleaned_content)
            
            # 2. 修复缺失的逗号
            cleaned_content = re.sub(r'}\s*{', '},{', cleaned_content)
            cleaned_content = re.sub(r']\s*\[', '],[', cleaned_content)
            
            # 3. 修复多余的逗号
            cleaned_content = re.sub(r',\s*}', '}', cleaned_content)
            cleaned_content = re.sub(r',\s*]', ']', cleaned_content)
            
            # 4. 确保数字不带引号
            cleaned_content = re.sub(r'"(\d+\.?\d*)"(?=\s*[,}\]])', r'\1', cleaned_content)
            
            # 尝试解析修复后的JSON
            return json.loads(cleaned_content)
            
        except Exception as e:
            logger.error(f"内置JSON修复失败: {e}")
            # 最后的回退：返回基本结构
            return {
                "table_type": "解析失败",
                "data": [],
                "confidence": 0.0,
                "error": str(e)
            }

    @staticmethod
    def _merge_results(
        template_result: TableProcessingResult,
        ai_result: TableProcessingResult
    ) -> TableProcessingResult:
        """
        合并模板和AI的提取结果
        """
        # 简单的合并策略：以模板结果为主，AI结果补充
        merged_data = template_result.extracted_data.copy()
        
        # 如果AI提取到了更多数据，进行智能合并
        for ai_row in ai_result.extracted_data:
            # 检查是否已存在相似数据
            is_duplicate = False
            for existing_row in merged_data:
                # 简单的重复检测逻辑
                if (existing_row.get("product_name") == ai_row.get("product_name") or
                    existing_row.get("商品名称") == ai_row.get("商品名称")):
                    is_duplicate = True
                    break
            
            if not is_duplicate:
                merged_data.append(ai_row)
        
        return TableProcessingResult(
            success=True,
            processing_method="hybrid",
            template_matched=template_result.template_matched,
            extracted_data=merged_data,
            column_mapping={**template_result.column_mapping, **ai_result.column_mapping},
            total_rows=len(merged_data),
            valid_rows=len(merged_data),
            error_rows=0,
            ai_processing_info=ai_result.ai_processing_info
        )

    @staticmethod
    async def _validate_and_correct_data(
        result: TableProcessingResult,
        request: TableProcessingRequest
    ) -> TableProcessingResult:
        """
        数据校验与修正
        """
        try:
            errors = []
            warnings = []
            corrections = []
            corrected_data = []
            
            for i, row in enumerate(result.extracted_data):
                corrected_row = row.copy()
                row_has_error = False
                
                # 基本数据验证和修正
                for key, value in row.items():
                    try:
                        # 数字字段验证和修正
                        if key in ["unit_price", "单价", "total", "合计", "quantity", "数量"]:
                            if isinstance(value, str):
                                # 清理数字字符串
                                import re
                                cleaned_value = re.sub(r'[^\d.-]', '', str(value))
                                if cleaned_value:
                                    corrected_row[key] = float(cleaned_value)
                                    if value != cleaned_value:
                                        corrections.append(TableCorrectionSuggestion(
                                            row_index=i,
                                            column=key,
                                            original_value=value,
                                            corrected_value=float(cleaned_value),
                                            confidence=0.9,
                                            correction_reason="数字格式标准化"
                                        ))
                                else:
                                    corrected_row[key] = 0
                                    errors.append(TableValidationError(
                                        row_index=i,
                                        column=key,
                                        error_type="invalid_number",
                                        error_message="无效的数字格式",
                                        original_value=value,
                                        suggested_value=0
                                    ))
                                    row_has_error = True
                        
                        # 文本字段验证
                        elif key in ["product_name", "商品名称"]:
                            if not value or str(value).strip() == "":
                                errors.append(TableValidationError(
                                    row_index=i,
                                    column=key,
                                    error_type="required_field_empty",
                                    error_message="商品名称不能为空",
                                    original_value=value
                                ))
                                row_has_error = True
                            else:
                                # 清理商品名称
                                cleaned_name = str(value).strip()
                                corrected_row[key] = cleaned_name
                                
                    except Exception as e:
                        logger.warning(f"验证字段 {key} 时出错: {e}")
                        warnings.append({
                            "row_index": i,
                            "column": key,
                            "message": f"字段验证出错: {str(e)}"
                        })
                
                if not row_has_error or not request.enable_correction:
                    corrected_data.append(corrected_row)
            
            # 更新结果
            result.extracted_data = corrected_data
            result.valid_rows = len(corrected_data)
            result.error_rows = result.total_rows - result.valid_rows
            result.errors.extend([error.model_dump() for error in errors])
            result.warnings.extend(warnings)
            
            # 添加修正信息
            if corrections:
                if not result.ai_processing_info:
                    result.ai_processing_info = {}
                result.ai_processing_info["corrections"] = [
                    correction.model_dump() for correction in corrections
                ]
            
            # Construct and return the new result with corrections and original AI info
            return TableProcessingResult(
                success=result.success,  # Preserve original success status
                processing_method=result.processing_method,  # Pass through original processing method
                template_matched=result.template_matched,  # Pass through original template match
                extracted_data=corrected_data, # Use the processed data
                column_mapping=result.column_mapping, # Assuming column_mapping should also be passed or is re-evaluated
                total_rows=len(corrected_data),
                valid_rows=len([r for r in corrected_data if not r.get('_has_error_', False)]),
                error_rows=len([r for r in corrected_data if r.get('_has_error_', False)]),
                errors=errors,  # Use errors accumulated in this method
                warnings=warnings, # Use warnings accumulated in this method
                # corrections_summary=corrections, # If there's a field for this
                ai_processing_info=result.ai_processing_info  # CRITICAL: Pass through original AI processing info
            )

        except Exception as e:
            logger.error(f"数据验证失败: {e}")
            result.errors.append({
                "type": "validation_error",
                "message": f"数据验证过程中出错: {str(e)}"
            })
            return result 