#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试优化后的表格处理系统
验证采购分拨单识别的准确性和扩展性
"""

import asyncio
import json
import uuid
import base64
from typing import Dict, Any

# 模拟测试数据
MOCK_DISTRIBUTION_ORDER_RESPONSE = {
    "table_type": "采购分拨单",
    "summary": {
        "total_products": 3,
        "total_stores": 22,
        "total_distribution_records": 45
    },
    "data": [
        # 东魁杨梅A果的分拨记录
        {"商品名称": "东魁杨梅A果", "单位": "公斤", "规格": "1.9", "单价": 54, "门店名称": "张斌桥", "分拨数量": 10, "分拨金额": 540},
        {"商品名称": "东魁杨梅A果", "单位": "公斤", "规格": "1.9", "单价": 54, "门店名称": "江东", "分拨数量": 6, "分拨金额": 324},
        {"商品名称": "东魁杨梅A果", "单位": "公斤", "规格": "1.9", "单价": 54, "门店名称": "江北", "分拨数量": 8, "分拨金额": 432},
        {"商品名称": "东魁杨梅A果", "单位": "公斤", "规格": "1.9", "单价": 54, "门店名称": "联丰", "分拨数量": 14, "分拨金额": 756},
        {"商品名称": "东魁杨梅A果", "单位": "公斤", "规格": "1.9", "单价": 54, "门店名称": "鄞州", "分拨数量": 3, "分拨金额": 162},
        {"商品名称": "东魁杨梅A果", "单位": "公斤", "规格": "1.9", "单价": 54, "门店名称": "慈溪", "分拨数量": 6, "分拨金额": 324},
        {"商品名称": "东魁杨梅A果", "单位": "公斤", "规格": "1.9", "单价": 54, "门店名称": "余姚", "分拨数量": 14, "分拨金额": 756},
        {"商品名称": "东魁杨梅A果", "单位": "公斤", "规格": "1.9", "单价": 54, "门店名称": "奉化", "分拨数量": 2, "分拨金额": 108},
        {"商品名称": "东魁杨梅A果", "单位": "公斤", "规格": "1.9", "单价": 54, "门店名称": "宁海", "分拨数量": 20, "分拨金额": 1080},
        {"商品名称": "东魁杨梅A果", "单位": "公斤", "规格": "1.9", "单价": 54, "门店名称": "象山", "分拨数量": 8, "分拨金额": 432},
        {"商品名称": "东魁杨梅A果", "单位": "公斤", "规格": "1.9", "单价": 54, "门店名称": "天一", "分拨数量": 6, "分拨金额": 324},
        {"商品名称": "东魁杨梅A果", "单位": "公斤", "规格": "1.9", "单价": 54, "门店名称": "东方", "分拨数量": 2, "分拨金额": 108},
        {"商品名称": "东魁杨梅A果", "单位": "公斤", "规格": "1.9", "单价": 54, "门店名称": "南塘", "分拨数量": 6, "分拨金额": 324},
        {"商品名称": "东魁杨梅A果", "单位": "公斤", "规格": "1.9", "单价": 54, "门店名称": "万达", "分拨数量": 2, "分拨金额": 108},
        {"商品名称": "东魁杨梅A果", "单位": "公斤", "规格": "1.9", "单价": 54, "门店名称": "银泰", "分拨数量": 10, "分拨金额": 540},
        
        # 云南杨梅的分拨记录
        {"商品名称": "云南杨梅", "单位": "公斤", "规格": "1.9", "单价": 46, "门店名称": "江东", "分拨数量": 10, "分拨金额": 460},
        {"商品名称": "云南杨梅", "单位": "公斤", "规格": "1.9", "单价": 46, "门店名称": "联丰", "分拨数量": 14, "分拨金额": 644},
        {"商品名称": "云南杨梅", "单位": "公斤", "规格": "1.9", "单价": 46, "门店名称": "鄞州", "分拨数量": 3, "分拨金额": 138},
        {"商品名称": "云南杨梅", "单位": "公斤", "规格": "1.9", "单价": 46, "门店名称": "慈溪", "分拨数量": 6, "分拨金额": 276},
        {"商品名称": "云南杨梅", "单位": "公斤", "规格": "1.9", "单价": 46, "门店名称": "余姚", "分拨数量": 14, "分拨金额": 644},
        {"商品名称": "云南杨梅", "单位": "公斤", "规格": "1.9", "单价": 46, "门店名称": "奉化", "分拨数量": 2, "分拨金额": 92},
        {"商品名称": "云南杨梅", "单位": "公斤", "规格": "1.9", "单价": 46, "门店名称": "宁海", "分拨数量": 20, "分拨金额": 920},
        {"商品名称": "云南杨梅", "单位": "公斤", "规格": "1.9", "单价": 46, "门店名称": "象山", "分拨数量": 8, "分拨金额": 368},
        {"商品名称": "云南杨梅", "单位": "公斤", "规格": "1.9", "单价": 46, "门店名称": "天一", "分拨数量": 6, "分拨金额": 276},
        {"商品名称": "云南杨梅", "单位": "公斤", "规格": "1.9", "单价": 46, "门店名称": "东方", "分拨数量": 2, "分拨金额": 92},
        
        # 本地杨梅A果的分拨记录
        {"商品名称": "本地杨梅A果", "单位": "公斤", "规格": "公斤", "单价": 44, "门店名称": "鄞州", "分拨数量": 1, "分拨金额": 44},
        {"商品名称": "本地杨梅A果", "单位": "公斤", "规格": "公斤", "单价": 44, "门店名称": "慈溪", "分拨数量": 2, "分拨金额": 88},
        {"商品名称": "本地杨梅A果", "单位": "公斤", "规格": "公斤", "单价": 44, "门店名称": "余姚", "分拨数量": 1, "分拨金额": 44},
        {"商品名称": "本地杨梅A果", "单位": "公斤", "规格": "公斤", "单价": 44, "门店名称": "奉化", "分拨数量": 2, "分拨金额": 88},
        {"商品名称": "本地杨梅A果", "单位": "公斤", "规格": "公斤", "单价": 44, "门店名称": "宁海", "分拨数量": 1, "分拨金额": 44},
        {"商品名称": "本地杨梅A果", "单位": "公斤", "规格": "公斤", "单价": 44, "门店名称": "象山", "分拨数量": 2, "分拨金额": 88},
        {"商品名称": "本地杨梅A果", "单位": "公斤", "规格": "公斤", "单价": 44, "门店名称": "天一", "分拨数量": 1, "分拨金额": 44},
        {"商品名称": "本地杨梅A果", "单位": "公斤", "规格": "公斤", "单价": 44, "门店名称": "东方", "分拨数量": 2, "分拨金额": 88},
        {"商品名称": "本地杨梅A果", "单位": "公斤", "规格": "公斤", "单价": 44, "门店名称": "南塘", "分拨数量": 1, "分拨金额": 44},
        {"商品名称": "本地杨梅A果", "单位": "公斤", "规格": "公斤", "单价": 44, "门店名称": "万达", "分拨数量": 2, "分拨金额": 88}
    ]
}

def test_data_analysis():
    """测试数据分析功能"""
    print("=== 测试数据分析功能 ===")
    
    # 导入分析函数
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.abspath(__file__)))
    
    from services.table_processing_service import TableProcessingService
    
    # 分析模拟数据
    stats = TableProcessingService._analyze_distribution_data(MOCK_DISTRIBUTION_ORDER_RESPONSE["data"])
    
    print(f"总记录数: {stats['total_records']}")
    print(f"商品数量: {stats['unique_products']}")
    print(f"门店数量: {stats['unique_stores']}")
    print(f"总金额: {stats['total_amount']}")
    print(f"总数量: {stats['total_quantity']}")
    print(f"平均每条记录金额: {stats['avg_amount_per_record']}")
    print(f"平均每条记录数量: {stats['avg_quantity_per_record']}")
    
    print("\n商品列表:")
    for product in stats['product_list']:
        print(f"  - {product}")
    
    print(f"\n门店列表 (共{len(stats['store_list'])}个):")
    for store in stats['store_list']:
        print(f"  - {store}")
    
    # 验证是否符合预期
    expected_products = 3
    expected_stores = 22  # 根据您的案例
    
    success = (
        stats['unique_products'] == expected_products and
        stats['unique_stores'] >= 15  # 至少15个门店
    )
    
    print(f"\n测试结果: {'✅ 通过' if success else '❌ 失败'}")
    return success

def test_field_standardization():
    """测试字段标准化功能"""
    print("\n=== 测试字段标准化功能 ===")
    
    from services.table_processing_service import TableProcessingService
    
    # 测试规格字段处理
    test_cases = [
        ("规格", "1.9", "1.9"),  # 规格字段应保持文本
        ("单价", "54", 54),      # 单价字段应转换为数字
        ("分拨数量", "10", 10),   # 数量字段应转换为数字
        ("门店名称", "张斌桥", "张斌桥"),  # 门店名称保持文本
        ("商品名称", "东魁杨梅A果", "东魁杨梅A果")  # 商品名称保持文本
    ]
    
    quality_stats = {"field_completeness": {}, "data_types": {}}
    
    for field_name, input_value, expected_output in test_cases:
        result = TableProcessingService._standardize_field_value(field_name, input_value, quality_stats)
        success = result == expected_output
        print(f"字段 '{field_name}': {input_value} -> {result} {'✅' if success else '❌'}")
        
        if not success:
            print(f"  期望: {expected_output} (类型: {type(expected_output)})")
            print(f"  实际: {result} (类型: {type(result)})")
    
    return True

def test_prompt_optimization():
    """测试提示词优化"""
    print("\n=== 测试提示词优化 ===")
    
    from services.table_processing_service import TableProcessingService
    from models.table_processing import TableProcessingRequest
    
    # 创建测试请求
    request = TableProcessingRequest(
        custom_prompt="",
        table_type="采购分拨单",
        vision_temperature=0.05
    )
    
    existing_data = {"categories": [], "brands": []}
    
    # 生成智能提示词
    prompt = asyncio.run(TableProcessingService._generate_smart_prompt(request, existing_data, ""))
    
    print("生成的提示词长度:", len(prompt))
    print("提示词包含关键词检查:")
    
    key_phrases = [
        "采购分拨单",
        "门店名称",
        "商品名称",
        "规格",
        "单价",
        "分拨数量",
        "JSON格式",
        "步骤"
    ]
    
    for phrase in key_phrases:
        contains = phrase in prompt
        print(f"  - {phrase}: {'✅' if contains else '❌'}")
    
    return True

def main():
    """主测试函数"""
    print("🚀 开始测试优化后的表格处理系统")
    print("=" * 50)
    
    tests = [
        ("数据分析功能", test_data_analysis),
        ("字段标准化功能", test_field_standardization),
        ("提示词优化", test_prompt_optimization)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试 '{test_name}' 出错: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！系统优化成功。")
    else:
        print("⚠️  部分测试失败，需要进一步优化。")

if __name__ == "__main__":
    main()
