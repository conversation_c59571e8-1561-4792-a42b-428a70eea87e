#!/usr/bin/env python3
"""
简化AI提示词测试
验证回归2025最佳实践后的效果
"""

import asyncio
import uuid
import logging
from typing import Dict, Any

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 测试配置
TEST_PROJECT_ID = "66e74880-0f4c-4b56-bcae-12691bdbb59e"
TEST_USER_ID = "01932a18-8a66-7f20-8e37-f39b29bef0a1"
TEST_FILE_ID = "d09595ab-5c4b-4231-bfd0-dc30e697d1b3"

async def test_simplified_ai_recognition():
    """测试简化后的AI识别效果"""
    
    print("=" * 80)
    print("🤖 测试简化AI提示词效果")
    print("=" * 80)
    
    try:
        # 导入必要的模块
        from tasks.enhanced_purchase_order_task_executor import EnhancedPurchaseOrderTaskExecutor
        from models.task import AsyncTask
        from uuid import UUID
        
        # 创建模拟任务
        mock_task = AsyncTask(
            id=str(uuid.uuid4()),
            project_id=UUID(TEST_PROJECT_ID),
            tenant_id=UUID(TEST_PROJECT_ID),
            user_id=UUID(TEST_USER_ID),
            task_type="purchase_order_ai_preview",
            task_name="简化AI测试任务",
            input_data={
                "file_id": TEST_FILE_ID,
                "upload_type": "both",
                "processing_mode": "auto"
            }
        )
        
        # 执行任务
        print("🔄 执行简化后的AI识别...")
        executor = EnhancedPurchaseOrderTaskExecutor()
        task_result = await executor.execute_task(mock_task, test_mode=True)
        
        if task_result.get('success'):
            frontend_data = task_result['data']
            purchase_items = frontend_data.get('preview', {}).get('purchase_items', [])
            distribution_destinations = frontend_data.get('preview', {}).get('distribution_destinations', [])
            
            print(f"✅ AI识别成功")
            print(f"   - 识别商品数: {len(purchase_items)}")
            print(f"   - 识别门店数: {len(distribution_destinations)}")
            
            # 重点检查董山门店的数据完整性
            dongshan_store = None
            for dest in distribution_destinations:
                if '董山' in dest.get('target_name', ''):
                    dongshan_store = dest
                    break
            
            if dongshan_store:
                dongshan_items = dongshan_store.get('items', [])
                print(f"\n🏪 董山门店数据检查:")
                print(f"   - 商品数量: {len(dongshan_items)}")
                print(f"   - 商品列表:")
                
                for i, item in enumerate(dongshan_items, 1):
                    product_name = item.get('product_name', '')
                    quantity = item.get('quantity', 0)
                    print(f"     {i}. {product_name} (数量: {quantity})")
                
                # 检查是否包含关键商品
                expected_products = ['东魁杨梅A果', '云南杨梅', '产地杨梅A果', '本地杨梅A果']
                found_products = [item.get('product_name', '') for item in dongshan_items]
                
                print(f"\n📊 关键商品检查:")
                for product in expected_products:
                    found = any(product in found_name for found_name in found_products)
                    print(f"   - {product}: {'✅ 找到' if found else '❌ 缺失'}")
                
                # 计算完整性
                completeness = len([p for p in expected_products if any(p in f for f in found_products)]) / len(expected_products) * 100
                print(f"\n📈 董山门店数据完整性: {completeness:.1f}%")
                
                if completeness >= 75:
                    print("🎉 简化AI提示词效果良好！")
                    return True
                else:
                    print("⚠️  数据完整性仍需改进")
                    return False
            else:
                print("❌ 未找到董山门店数据")
                return False
        else:
            print(f"❌ AI识别失败: {task_result.get('error')}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def analyze_prompt_complexity():
    """分析提示词复杂度"""
    
    print("\n" + "=" * 80)
    print("📝 提示词复杂度分析")
    print("=" * 80)
    
    try:
        from services.enhanced_table_processing_service import EnhancedTableProcessingService
        import inspect
        
        # 获取门店批次数据提取方法的源码
        source = inspect.getsource(EnhancedTableProcessingService._extract_store_batch_data)
        
        # 分析提示词特征
        prompt_indicators = {
            '硬编码规则': ['【', '】', '严格按照', '绝对不要'],
            '过度具体化': ['示例', '如果表格某行为', '则应输出'],
            '复杂格式': ['字段映射规则', '重要提醒', '计算公式说明'],
            '简化特征': ['请提取', '观察表格', '返回JSON格式']
        }
        
        print("🔍 提示词特征分析:")
        for category, indicators in prompt_indicators.items():
            count = sum(1 for indicator in indicators if indicator in source)
            total = len(indicators)
            percentage = count / total * 100
            
            status = "✅ 良好" if category == '简化特征' and percentage > 50 else \
                    "⚠️  需要改进" if category != '简化特征' and percentage > 30 else \
                    "✅ 已优化"
            
            print(f"   - {category}: {count}/{total} ({percentage:.1f}%) {status}")
        
        # 检查是否成功简化
        complex_indicators = sum(len([i for i in indicators if i in source]) 
                               for category, indicators in prompt_indicators.items() 
                               if category != '简化特征')
        simple_indicators = len([i for i in prompt_indicators['简化特征'] if i in source])
        
        if simple_indicators > complex_indicators:
            print("\n🎉 提示词已成功简化，符合2025最佳实践！")
            return True
        else:
            print("\n⚠️  提示词仍然过于复杂，需要进一步简化")
            return False
            
    except Exception as e:
        print(f"❌ 分析失败: {str(e)}")
        return False

async def main():
    """主测试函数"""
    
    print("🚀 开始简化AI提示词测试")
    
    # 测试1: 提示词复杂度分析
    prompt_ok = analyze_prompt_complexity()
    
    # 测试2: AI识别效果测试
    recognition_ok = await test_simplified_ai_recognition()
    
    # 总结
    print("\n" + "=" * 80)
    print("📋 简化测试结果总结")
    print("=" * 80)
    
    print(f"✅ 提示词简化: {'通过' if prompt_ok else '失败'}")
    print(f"✅ AI识别效果: {'通过' if recognition_ok else '失败'}")
    
    if prompt_ok and recognition_ok:
        print("\n🎉 简化AI提示词成功！回归2025最佳实践完成。")
        print("\n💡 关键改进:")
        print("   - 移除过度具体化的限定词")
        print("   - 简化字段映射规则")
        print("   - 减少硬编码约束")
        print("   - 增强AI语义理解能力")
        return True
    else:
        print("\n⚠️  简化效果需要进一步优化。")
        return False

if __name__ == "__main__":
    asyncio.run(main()) 