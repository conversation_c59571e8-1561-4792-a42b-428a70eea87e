#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试统一字段名称后的表格处理
验证前后端参数一致性
"""

import asyncio
import uuid
import json
from typing import Dict, Any

def test_field_name_consistency():
    """测试字段名称一致性"""
    print("🔧 测试字段名称一致性")
    
    # 前端期望的字段名称（英文）
    frontend_expected_fields = {
        "product_name": "商品名称",
        "product_specification": "规格", 
        "product_unit": "单位",
        "unit_price": "单价",
        "quantity": "数量",
        "total_amount": "金额",
        "store_name": "门店名称",
        "supplier_name": "供应商名称"
    }
    
    # 后端AI提示词中使用的字段名称
    backend_prompt_fields = {
        "product_name": "商品名称",
        "product_unit": "单位", 
        "product_specification": "规格",
        "unit_price": "单价",
        "store_name": "门店名称",
        "quantity": "数量",
        "total_amount": "金额"
    }
    
    print("前端期望字段:")
    for en_field, cn_desc in frontend_expected_fields.items():
        print(f"  {en_field}: {cn_desc}")
    
    print("\n后端提示词字段:")
    for en_field, cn_desc in backend_prompt_fields.items():
        print(f"  {en_field}: {cn_desc}")
    
    # 检查一致性
    common_fields = set(frontend_expected_fields.keys()) & set(backend_prompt_fields.keys())
    frontend_only = set(frontend_expected_fields.keys()) - set(backend_prompt_fields.keys())
    backend_only = set(backend_prompt_fields.keys()) - set(frontend_expected_fields.keys())
    
    print(f"\n一致性分析:")
    print(f"  共同字段: {len(common_fields)} 个")
    print(f"  前端独有: {len(frontend_only)} 个 - {frontend_only}")
    print(f"  后端独有: {len(backend_only)} 个 - {backend_only}")
    
    # 验证核心字段是否一致
    core_fields = ["product_name", "product_specification", "unit_price", "quantity", "store_name"]
    core_consistency = all(field in backend_prompt_fields for field in core_fields)
    
    print(f"\n核心字段一致性: {'✅ 通过' if core_consistency else '❌ 失败'}")
    
    return core_consistency

def test_field_standardization():
    """测试字段标准化功能"""
    print("\n🔧 测试字段标准化功能")
    
    try:
        import sys
        sys.path.append('/app')
        
        from services.table_processing_service import TableProcessingService
        
        # 测试英文字段名的标准化
        test_cases = [
            ("product_name", "测试商品", str),
            ("product_specification", "500g", str),  # 规格应保持文本
            ("unit_price", "10.5", (int, float)),    # 单价应转换为数字
            ("quantity", "5", (int, float)),         # 数量应转换为数字
            ("store_name", "测试门店", str),
            ("total_amount", "52.5", (int, float)),  # 金额应转换为数字
        ]
        
        quality_stats = {"field_completeness": {}, "data_types": {}}
        
        all_passed = True
        for field_name, input_value, expected_type in test_cases:
            result = TableProcessingService._standardize_field_value(
                field_name, input_value, quality_stats
            )
            
            if isinstance(expected_type, tuple):
                type_match = type(result) in expected_type
            else:
                type_match = isinstance(result, expected_type)
            
            status = "✅" if type_match else "❌"
            print(f"  {field_name}: '{input_value}' -> {result} ({type(result).__name__}) {status}")
            
            if not type_match:
                all_passed = False
                print(f"    期望类型: {expected_type}, 实际类型: {type(result)}")
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 字段标准化测试失败: {e}")
        return False

def test_ai_prompt_generation():
    """测试AI提示词生成"""
    print("\n🔧 测试AI提示词生成")
    
    try:
        import sys
        sys.path.append('/app')
        
        from services.table_processing_service import TableProcessingService
        
        # 模拟请求对象
        class MockRequest:
            def __init__(self, table_type):
                self.custom_prompt = ""
                self.table_type = table_type
        
        # 测试不同表格类型的提示词
        table_types = ["采购分拨单", "采购单", "库存表"]
        
        for table_type in table_types:
            request = MockRequest(table_type)
            existing_data = {}
            
            try:
                # 生成提示词
                prompt = asyncio.run(TableProcessingService._generate_smart_prompt(
                    request, existing_data, ""
                ))
                
                print(f"\n{table_type} 提示词检查:")
                
                # 检查是否包含英文字段名
                english_fields = [
                    "product_name", "product_specification", "product_unit", 
                    "unit_price", "quantity", "total_amount"
                ]
                
                field_count = 0
                for field in english_fields:
                    if field in prompt:
                        field_count += 1
                
                print(f"  包含英文字段: {field_count}/{len(english_fields)} 个")
                print(f"  提示词长度: {len(prompt)} 字符")
                
                # 检查特定字段
                if table_type == "采购分拨单":
                    has_store_field = "store_name" in prompt
                    print(f"  包含门店字段: {'✅' if has_store_field else '❌'}")
                
                if table_type == "采购单":
                    has_supplier_field = "supplier_name" in prompt
                    print(f"  包含供应商字段: {'✅' if has_supplier_field else '❌'}")
                
            except Exception as e:
                print(f"  ❌ 生成提示词失败: {e}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 提示词生成测试失败: {e}")
        return False

def test_mock_ai_response_parsing():
    """测试模拟AI响应解析"""
    print("\n🔧 测试模拟AI响应解析")
    
    # 模拟AI返回的统一字段名称数据
    mock_ai_response = {
        "table_type": "采购分拨单",
        "data": [
            {
                "product_name": "东魁杨梅A果",
                "product_unit": "公斤",
                "product_specification": "1.9",
                "unit_price": 54,
                "store_name": "张斌桥",
                "quantity": 10,
                "total_amount": 540
            },
            {
                "product_name": "东魁杨梅A果", 
                "product_unit": "公斤",
                "product_specification": "1.9",
                "unit_price": 54,
                "store_name": "江东",
                "quantity": 6,
                "total_amount": 324
            },
            {
                "product_name": "云南杨梅",
                "product_unit": "公斤", 
                "product_specification": "1.9",
                "unit_price": 46,
                "store_name": "联丰",
                "quantity": 14,
                "total_amount": 644
            }
        ]
    }
    
    print("模拟AI响应数据:")
    print(f"  表格类型: {mock_ai_response['table_type']}")
    print(f"  数据记录数: {len(mock_ai_response['data'])}")
    
    # 验证字段名称
    if mock_ai_response['data']:
        first_record = mock_ai_response['data'][0]
        print(f"  字段名称: {list(first_record.keys())}")
        
        # 检查是否使用英文字段名
        english_fields = ["product_name", "product_specification", "unit_price", "quantity", "store_name"]
        english_field_count = sum(1 for field in english_fields if field in first_record)
        
        print(f"  英文字段覆盖: {english_field_count}/{len(english_fields)} 个")
        
        # 验证数据类型
        print(f"  数据类型验证:")
        print(f"    product_name: {type(first_record.get('product_name')).__name__}")
        print(f"    product_specification: {type(first_record.get('product_specification')).__name__}")
        print(f"    unit_price: {type(first_record.get('unit_price')).__name__}")
        print(f"    quantity: {type(first_record.get('quantity')).__name__}")
        
        # 检查数字字段是否为数字类型
        numeric_fields_correct = (
            isinstance(first_record.get('unit_price'), (int, float)) and
            isinstance(first_record.get('quantity'), (int, float)) and
            isinstance(first_record.get('total_amount'), (int, float))
        )
        
        print(f"  数字字段类型正确: {'✅' if numeric_fields_correct else '❌'}")
        
        return english_field_count >= 4 and numeric_fields_correct
    
    return False

async def main():
    """主测试函数"""
    print("🚀 开始统一字段名称测试")
    print("=" * 60)
    
    tests = [
        ("字段名称一致性", test_field_name_consistency, False),
        ("字段标准化功能", test_field_standardization, False),
        ("AI提示词生成", test_ai_prompt_generation, False),
        ("模拟AI响应解析", test_mock_ai_response_parsing, False),
    ]
    
    results = []
    for test_name, test_func, is_async in tests:
        print(f"\n📋 执行测试: {test_name}")
        try:
            if is_async:
                result = await test_func()
            else:
                result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试 '{test_name}' 执行出错: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 60)
    print("📊 测试结果汇总:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！字段名称统一成功。")
        print("\n✨ 统一效果:")
        print("  - 前后端使用相同的英文字段名")
        print("  - AI提示词生成英文字段格式")
        print("  - 字段标准化支持英文字段名")
        print("  - 数据类型处理保持正确性")
    else:
        print("⚠️ 部分测试失败，需要进一步调整。")

if __name__ == "__main__":
    asyncio.run(main())
